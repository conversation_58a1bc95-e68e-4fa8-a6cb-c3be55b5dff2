<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Woodworking Cut Optimizer</title>
<script src="https://cdn.jsdelivr.net/gh/jakesgordon/bin-packing@master/js/packer.js"></script>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
          Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f4f7f9;
        color: #333;
        display: flex;
        flex-direction: column;
        min-height: 100vh;
      }

      .header {
        background-color: #2c3e50;
        color: white;
        padding: 1rem 1.5rem;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .header h1 {
        margin: 0;
        font-size: 1.8rem;
      }

      /* New Layout System */
      .app-layout {
        display: flex;
        min-height: calc(100vh - 80px); /* Account for header */
      }

      .app-sidebar {
        width: 280px;
        background: #2c3e50;
        color: white;
        overflow-y: auto;
        flex-shrink: 0;
      }

      .app-content {
        flex: 1;
        background: #f8f9fa;
        overflow-y: auto;
        padding: 1.5rem;
      }

      /* Sidebar Styles */
      .sidebar {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .sidebar-header {
        padding: 1.5rem 1rem;
        border-bottom: 1px solid #34495e;
      }

      .sidebar-header h1 {
        font-size: 1.2rem;
        margin: 0;
        color: #ecf0f1;
        font-weight: 600;
      }

      .sidebar-nav {
        padding: 1rem 0;
        border-bottom: 1px solid #34495e;
      }

      .nav-button {
        width: 100%;
        background: none;
        border: none;
        color: #bdc3c7;
        padding: 0.75rem 1rem;
        text-align: left;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 0.95rem;
      }

      .nav-button:hover {
        background: #34495e;
        color: #ecf0f1;
      }

      .nav-button.active {
        background: #3498db;
        color: white;
      }

      .nav-icon {
        font-size: 1.1rem;
      }

      .projects-section {
        flex: 1;
        padding: 1rem;
        overflow-y: auto;
      }

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
      }

      .section-header h3 {
        margin: 0;
        font-size: 1rem;
        color: #ecf0f1;
      }

      .create-project-btn {
        background: #27ae60;
        color: white;
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
        cursor: pointer;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.2s ease;
      }

      .create-project-btn:hover {
        background: #2ecc71;
      }

      .projects-list {
        max-height: 300px;
        overflow-y: auto;
      }

      .project-item {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        background: #34495e;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .project-item:hover {
        background: #3498db;
      }

      .project-item.active {
        background: #e74c3c;
      }

      .project-name {
        display: block;
        font-weight: 500;
        color: #ecf0f1;
        margin-bottom: 0.25rem;
      }

      .project-meta {
        font-size: 0.8rem;
        color: #95a5a6;
      }

      .empty-state {
        text-align: center;
        padding: 2rem 1rem;
        color: #95a5a6;
      }

      .sidebar-footer {
        padding: 1rem;
        border-top: 1px solid #34495e;
        text-align: center;
      }

      .sidebar-footer p {
        margin: 0;
        font-size: 0.8rem;
        color: #95a5a6;
      }

      /* Content Area Styles */
      .content-view {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        min-height: calc(100vh - 140px);
      }

      /* Tab Container Styles */
      .tab-container {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .tab-header {
        border-bottom: 1px solid #e1e8ed;
        background: #f8f9fa;
        border-radius: 8px 8px 0 0;
      }

      .tab-list {
        display: flex;
        padding: 0;
        margin: 0;
      }

      .tab-button {
        background: none;
        border: none;
        padding: 1rem 1.5rem;
        cursor: pointer;
        font-size: 0.95rem;
        font-weight: 500;
        color: #6c757d;
        border-bottom: 3px solid transparent;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .tab-button:hover {
        color: #495057;
        background: #e9ecef;
      }

      .tab-button.active {
        color: #3498db;
        border-bottom-color: #3498db;
        background: white;
      }

      .tab-icon {
        font-size: 1.1rem;
      }

      .tab-content {
        flex: 1;
        overflow-y: auto;
      }

      .tab-panel {
        height: 100%;
        padding: 1.5rem;
      }

      /* Project Detail View Styles */
      .project-detail-view {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .project-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e1e8ed;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .project-info h1 {
        margin: 0 0 0.5rem 0;
        color: #2c3e50;
        font-size: 1.5rem;
      }

      .project-meta {
        display: flex;
        gap: 1rem;
        font-size: 0.9rem;
        color: #6c757d;
      }

      .project-actions {
        display: flex;
        gap: 0.75rem;
        align-items: center;
      }

      .project-content {
        flex: 1;
        overflow: hidden;
      }

      .tab-container-wrapper {
        height: 100%;
      }

      /* Inventory View Styles */
      .inventory-view {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .inventory-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e1e8ed;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }

      .header-content h1 {
        margin: 0 0 0.5rem 0;
        color: #2c3e50;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .header-description {
        margin: 0;
        color: #6c757d;
        font-size: 0.95rem;
        max-width: 500px;
      }

      .header-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
      }

      .material-count {
        text-align: center;
        padding: 0.5rem 1rem;
        background: #e3f2fd;
        border-radius: 6px;
        border: 1px solid #bbdefb;
      }

      .count-number {
        display: block;
        font-size: 1.5rem;
        font-weight: bold;
        color: #1976d2;
      }

      .count-label {
        font-size: 0.8rem;
        color: #1565c0;
      }

      .inventory-content {
        flex: 1;
        overflow-y: auto;
        padding: 1.5rem;
        display: flex;
        flex-direction: column;
        gap: 2rem;
      }

      .material-form-section,
      .inventory-list-section {
        background: white;
        border: 1px solid #e1e8ed;
        border-radius: 8px;
        padding: 1.5rem;
      }

      h2 {
        color: #34495e;
        border-bottom: 2px solid #e0e0e0;
        padding-bottom: 0.5rem;
        margin-top: 0;
        font-size: 1.3rem;
      }

      .form-group {
        margin-bottom: 1rem;
      }

      .form-group label {
        display: block;
        margin-bottom: 0.3rem;
        font-weight: 600;
        color: #555;
        font-size: 0.9rem;
      }

      .form-group input[type="text"],
      .form-group input[type="number"],
      .form-group select {
        width: calc(100% - 1rem);
        padding: 0.6rem 0.5rem;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-sizing: border-box;
        font-size: 0.9rem;
      }

      .form-group input:focus,
      .form-group select:focus {
        border-color: #3498db;
        outline: none;
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
      }

      .inline-inputs {
        display: flex;
        gap: 0.5rem;
        align-items: center;
      }

      .inline-inputs .form-group {
        flex: 1;
        margin-bottom: 0;
      }

      .inline-inputs input[type="number"] {
        width: calc(100% - 1rem); /* Adjust for padding */
      }
      .inline-inputs select {
        width: 100%;
      }

      button {
        background-color: #3498db;
        color: white;
        padding: 0.7rem 1.2rem;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.95rem;
        transition: background-color 0.2s ease;
        font-weight: 500;
      }

      button:hover {
        background-color: #2980b9;
      }

      button.secondary {
        background-color: #e74c3c;
      }
      button.secondary:hover {
        background-color: #c0392b;
      }

      button.action-btn {
        background-color: #2ecc71;
      }
      button.action-btn:hover {
        background-color: #27ae60;
      }

      ul {
        list-style-type: none;
        padding: 0;
      }

      li {
        background-color: #ecf0f1;
        padding: 0.6rem 0.8rem;
        margin-bottom: 0.5rem;
        border-radius: 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.9rem;
      }

      li button {
        padding: 0.2rem 0.5rem;
        font-size: 0.8rem;
        background-color: #95a5a6;
      }
      li button:hover {
        background-color: #7f8c8d;
      }

      .item-buttons {
        display: flex;
        gap: 0.5rem;
      }

      .item-buttons button {
        padding: 0.2rem 0.5rem;
        font-size: 0.8rem;
      }

      .edit-material-btn, .edit-piece-btn {
        background-color: #f39c12 !important;
      }
      .edit-material-btn:hover, .edit-piece-btn:hover {
        background-color: #e67e22 !important;
      }

      .danger-btn {
        background-color: #e74c3c !important;
      }
      .danger-btn:hover {
        background-color: #c0392b !important;
      }

      #visualizationContainer {
        border: 1px solid #bdc3c7;
        width: 100%;
        height: auto;
        aspect-ratio: 16 / 9; /* Default, can be dynamic */
        background-color: #fff;
        max-height: 500px; /* Limit container height */
        position: relative;
      }

      .canvas-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        position: relative;
      }

      .sheet-navigation {
        display: flex;
        gap: 0.5rem;
        align-items: center;
        margin-top: 0.5rem;
      }

      .piece-info-panel {
        transition: opacity 0.2s ease-in-out;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(4px);
      }

      .piece-info-panel:hover {
        opacity: 0.9;
      }

      #cuttingListOutput {
        width: 100%;
        height: 200px;
        font-family: "Courier New", Courier, monospace;
        font-size: 0.85rem;
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 0.5rem;
        white-space: pre-wrap;
        overflow-y: auto;
        background-color: #fdfdfd;
      }

      .footer {
        text-align: center;
        padding: 0.8rem;
        background-color: #34495e;
        color: #ecf0f1;
        font-size: 0.8rem;
      }

      /* Loading and Error States */
      .loading-state,
      .error-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        min-height: 400px;
        text-align: center;
        color: #6c757d;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e9ecef;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .error-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
      }

      .retry-btn {
        margin-top: 1rem;
        background: #3498db;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 4px;
        cursor: pointer;
      }

      /* Responsive */
      @media (max-width: 1024px) {
        .app-layout {
          flex-direction: column;
        }

        .app-sidebar {
          width: 100%;
          height: auto;
          order: 2;
        }

        .app-content {
          order: 1;
          padding: 1rem;
        }

        .project-header,
        .inventory-header {
          flex-direction: column;
          gap: 1rem;
          align-items: flex-start;
        }

        .project-actions,
        .header-actions {
          width: 100%;
          justify-content: flex-start;
        }
      }

      @media (max-width: 768px) {
        .inline-inputs {
          flex-direction: column;
          gap: 0.8rem;
          align-items: stretch;
        }

        .inline-inputs .form-group {
          margin-bottom: 0.8rem;
        }

        .tab-button {
          padding: 0.75rem 1rem;
          font-size: 0.9rem;
        }

        .project-actions,
        .header-actions {
          flex-wrap: wrap;
          gap: 0.5rem;
        }

        .sidebar-header h1 {
          font-size: 1rem;
        }

        .projects-section {
          padding: 0.5rem;
        }
      }

      /* Additional styles for new components */
      .projects-overview {
        padding: 2rem;
        text-align: center;
      }

      .projects-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
      }

      .project-card {
        background: white;
        border: 1px solid #e1e8ed;
        border-radius: 8px;
        padding: 1.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: left;
      }

      .project-card:hover {
        border-color: #3498db;
        box-shadow: 0 4px 12px rgba(52, 152, 219, 0.15);
        transform: translateY(-2px);
      }

      .project-card h3 {
        margin: 0 0 0.5rem 0;
        color: #2c3e50;
        font-size: 1.2rem;
      }

      .project-card p {
        margin: 0 0 0.5rem 0;
        color: #6c757d;
      }

      .project-card small {
        color: #95a5a6;
        font-size: 0.85rem;
      }

      /* Material cards in inventory */
      .materials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
      }

      .material-card {
        background: white;
        border: 1px solid #e1e8ed;
        border-radius: 6px;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .material-card:hover {
        border-color: #3498db;
        box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
      }

      .material-card.highlighted {
        border-color: #f39c12;
        box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.2);
        animation: highlight-pulse 1s ease-in-out;
      }

      @keyframes highlight-pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.02); }
      }

      .material-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 0.75rem;
      }

      .material-name {
        margin: 0;
        font-size: 1rem;
        color: #2c3e50;
        font-weight: 600;
      }

      .material-actions {
        display: flex;
        gap: 0.25rem;
      }

      .action-btn {
        background: none;
        border: 1px solid #e1e8ed;
        border-radius: 4px;
        padding: 0.25rem 0.5rem;
        cursor: pointer;
        font-size: 0.8rem;
        transition: all 0.2s ease;
      }

      .action-btn:hover {
        background: #f8f9fa;
        border-color: #3498db;
      }

      .edit-btn:hover { border-color: #3498db; }
      .duplicate-btn:hover { border-color: #27ae60; }
      .delete-btn:hover { border-color: #e74c3c; }

      .material-details {
        margin-bottom: 0.75rem;
      }

      .detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
      }

      .detail-label {
        color: #6c757d;
        font-weight: 500;
      }

      .detail-value {
        color: #2c3e50;
      }

      .material-footer {
        border-top: 1px solid #f1f3f4;
        padding-top: 0.5rem;
      }

      .material-id {
        color: #95a5a6;
        font-family: monospace;
      }

      /* Cutting list and visualization tabs */
      .cutting-list-tab,
      .visualization-tab {
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
      }

      .pieces-section,
      .cutting-list-section,
      .visualization-section {
        background: white;
        border: 1px solid #e1e8ed;
        border-radius: 8px;
        padding: 1.5rem;
      }

      .cutting-list-section {
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      .cutting-list-output {
        flex: 1;
        min-height: 300px;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        line-height: 1.4;
        border: 1px solid #e1e8ed;
        border-radius: 4px;
        padding: 1rem;
        resize: vertical;
      }

      .visualization-section {
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      .canvas-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 400px;
      }

      .visualization-container {
        flex: 1;
        border: 1px solid #e1e8ed;
        border-radius: 4px;
        background: #fafbfc;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 350px;
      }

      .sheet-navigation {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-top: 1rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 4px;
      }

      .prev-sheet-btn,
      .next-sheet-btn {
        background: #3498db;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: background-color 0.2s ease;
      }

      .prev-sheet-btn:hover,
      .next-sheet-btn:hover {
        background: #2980b9;
      }

      .prev-sheet-btn:disabled,
      .next-sheet-btn:disabled {
        background: #bdc3c7;
        cursor: not-allowed;
      }

      .sheet-indicator {
        font-weight: 500;
        color: #2c3e50;
        min-width: 120px;
        text-align: center;
      }

      .optimization-message {
        margin-top: 1rem;
        padding: 0.75rem;
        background: #e8f4fd;
        border: 1px solid #bee5eb;
        border-radius: 4px;
        color: #0c5460;
        text-align: center;
        font-style: italic;
      }
    </style>
  </head>
  <body>
    <header class="header">
      <h1>Woodworking Cut Optimizer</h1>
    </header>

    <div id="appContainer">
      <!-- New layout structure will be rendered here by the LayoutManager -->
    </div>

    <script type="module" src="index.tsx"></script>
  </body>
</html>
