<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Cutting List Test</title>
</head>
<body>
    <h1>Simple Cutting List Test</h1>
    <div id="status">Testing...</div>
    <textarea id="cuttingListOutput" style="width: 100%; height: 300px; font-family: monospace;"></textarea>
    
    <script type="module">
        // Test the cutting list generation directly
        function testCuttingListGeneration() {
            const statusEl = document.getElementById('status');
            const cuttingListEl = document.getElementById('cuttingListOutput');
            
            try {
                // Mock data for testing
                const mockLayouts = [
                    {
                        sheetId: 'sheet1',
                        baseMaterial: {
                            id: 'mat1',
                            name: 'Plywood 18mm',
                            length: 1200,
                            width: 800,
                            unit: 'mm',
                            quantity: 1,
                            thickness: '18mm'
                        },
                        pieces: [
                            {
                                id: 'piece1',
                                name: 'Shelf',
                                length: 600,
                                width: 300,
                                unit: 'mm',
                                quantity: 1,
                                grainDirection: 'length',
                                x: 0,
                                y: 0,
                                packedWidth: 305,
                                packedHeight: 605,
                                sheetId: 'sheet1',
                                color: '#e74c3c'
                            },
                            {
                                id: 'piece2',
                                name: 'Side Panel',
                                length: 400,
                                width: 200,
                                unit: 'mm',
                                quantity: 1,
                                grainDirection: 'width',
                                x: 305,
                                y: 0,
                                packedWidth: 205,
                                packedHeight: 405,
                                sheetId: 'sheet1',
                                color: '#3498db'
                            }
                        ],
                        widthUsed: 800,
                        heightUsed: 1200
                    }
                ];
                
                // Generate cutting list
                const cuttingList = generateCuttingList(mockLayouts);
                
                // Display result
                cuttingListEl.value = cuttingList;
                statusEl.textContent = 'SUCCESS: Cutting list generated!';
                statusEl.style.color = 'green';
                
            } catch (error) {
                statusEl.textContent = `ERROR: ${error.message}`;
                statusEl.style.color = 'red';
                console.error('Test error:', error);
            }
        }
        
        // Simplified cutting list generation function (copied from main.ts)
        function generateCuttingList(layouts) {
            if (layouts.length === 0) {
                return '';
            }

            let cuttingList = 'CUTTING LIST\n';
            cuttingList += '='.repeat(50) + '\n\n';

            layouts.forEach((layout, sheetIndex) => {
                cuttingList += `SHEET ${sheetIndex + 1}: ${layout.baseMaterial.name}\n`;
                cuttingList += `Material: ${layout.baseMaterial.length}${layout.baseMaterial.unit} × ${layout.baseMaterial.width}${layout.baseMaterial.unit}\n`;
                if (layout.baseMaterial.thickness) {
                    cuttingList += `Thickness: ${layout.baseMaterial.thickness}\n`;
                }
                cuttingList += '-'.repeat(30) + '\n';

                // Group pieces by name for easier cutting
                const piecesByName = layout.pieces.reduce((groups, piece) => {
                    const key = piece.name;
                    if (!groups[key]) {
                        groups[key] = [];
                    }
                    groups[key].push(piece);
                    return groups;
                }, {});

                Object.entries(piecesByName).forEach(([pieceName, pieces]) => {
                    const firstPiece = pieces[0];
                    cuttingList += `${pieces.length}x ${pieceName}\n`;
                    cuttingList += `  Size: ${firstPiece.length}${firstPiece.unit} × ${firstPiece.width}${firstPiece.unit}\n`;
                    if (firstPiece.grainDirection !== 'none') {
                        cuttingList += `  Grain: ${formatGrainDirection(firstPiece.grainDirection)}\n`;
                    }
                    cuttingList += '\n';
                });

                cuttingList += '\n';
            });

            // Add summary
            const totalPieces = layouts.reduce((sum, layout) => sum + layout.pieces.length, 0);
            const totalSheets = layouts.length;

            cuttingList += 'SUMMARY\n';
            cuttingList += '='.repeat(20) + '\n';
            cuttingList += `Total pieces: ${totalPieces}\n`;
            cuttingList += `Total sheets: ${totalSheets}\n`;

            return cuttingList;
        }
        
        function formatGrainDirection(direction) {
            switch (direction) {
                case 'length': return 'Along Length';
                case 'width': return 'Along Width';
                case 'none': return 'No Preference';
                default: return direction;
            }
        }
        
        // Run test
        testCuttingListGeneration();
    </script>
</body>
</html>
