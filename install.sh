#!/bin/bash

# Woodworking Cut Optimizer - Installation Script
# This script helps set up the development environment

set -e

echo "🔨 Woodworking Cut Optimizer - Setup Script"
echo "============================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not available"
    exit 1
fi

echo "✅ npm $(npm -v) detected"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
npm install

# Check if PostgreSQL is available (optional)
if command -v psql &> /dev/null; then
    echo "✅ PostgreSQL detected"
else
    echo "⚠️  PostgreSQL not detected. You'll need to:"
    echo "   1. Install PostgreSQL locally, or"
    echo "   2. Use a cloud database service like Supabase"
fi

# Create environment file if it doesn't exist
if [ ! -f .env.local ]; then
    echo ""
    echo "📝 Creating environment file..."
    cp .env.example .env.local
    echo "✅ Created .env.local from .env.example"
    echo "⚠️  Please edit .env.local with your database and Supabase credentials"
else
    echo "✅ .env.local already exists"
fi

# Generate Prisma client
echo ""
echo "🗄️  Setting up database client..."
npm run db:generate

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env.local with your database credentials"
echo "2. Set up your database:"
echo "   - For development: npm run db:push"
echo "   - For production: npm run db:migrate"
echo "3. Start the development server: npm run dev"
echo ""
echo "📖 For detailed setup instructions, see SETUP_GUIDE.md"
echo ""
echo "🚀 Happy woodworking!"
