<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test SVG Export</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
</head>
<body>
    <h1>SVG Export Test</h1>
    <div id="status">Testing SVG export...</div>
    <div id="container" style="width: 800px; height: 600px; border: 1px solid #ccc; margin: 20px 0;"></div>
    <button id="exportBtn">Export SVG</button>
    <button id="printBtn">Test Print</button>
    
    <script>
        // Create a simple D3 SVG for testing
        const container = d3.select('#container');
        const svg = container.append('svg')
            .attr('width', '100%')
            .attr('height', '100%')
            .attr('viewBox', '0 0 800 600')
            .style('background-color', '#fff')
            .style('border', '1px solid #bdc3c7');

        // Add some test content
        svg.append('rect')
            .attr('x', 50)
            .attr('y', 50)
            .attr('width', 700)
            .attr('height', 500)
            .style('fill', '#fafafa')
            .style('stroke', '#333')
            .style('stroke-width', 2);

        // Add test pieces
        const pieces = [
            { x: 100, y: 100, width: 200, height: 150, color: '#e74c3c', name: 'Shelf' },
            { x: 350, y: 100, width: 150, height: 200, color: '#3498db', name: 'Side Panel' },
            { x: 100, y: 300, width: 300, height: 100, color: '#2ecc71', name: 'Back Panel' }
        ];

        pieces.forEach(piece => {
            const group = svg.append('g');
            
            // Draw piece
            group.append('rect')
                .attr('x', piece.x)
                .attr('y', piece.y)
                .attr('width', piece.width)
                .attr('height', piece.height)
                .style('fill', piece.color)
                .style('stroke', '#333')
                .style('stroke-width', 1);
            
            // Add label
            group.append('text')
                .attr('x', piece.x + piece.width / 2)
                .attr('y', piece.y + piece.height / 2)
                .attr('text-anchor', 'middle')
                .attr('dominant-baseline', 'middle')
                .style('font-size', '14px')
                .style('font-weight', 'bold')
                .style('fill', '#fff')
                .text(piece.name);
        });

        // Add title
        svg.append('text')
            .attr('x', 400)
            .attr('y', 30)
            .attr('text-anchor', 'middle')
            .style('font-size', '18px')
            .style('font-weight', 'bold')
            .text('Test Sheet Layout');

        // Export functionality
        function exportSVGAsString() {
            const svgElement = svg.node();
            if (!svgElement) return '';

            const clonedSvg = svgElement.cloneNode(true);
            clonedSvg.setAttribute('width', '800');
            clonedSvg.setAttribute('height', '600');
            clonedSvg.setAttribute('viewBox', '0 0 800 600');

            return new XMLSerializer().serializeToString(clonedSvg);
        }

        // Test export
        document.getElementById('exportBtn').addEventListener('click', () => {
            const svgString = exportSVGAsString();
            
            // Create HTML content
            const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>Exported SVG</title>
    <style>
        body { margin: 20px; font-family: Arial, sans-serif; }
        .svg-container { text-align: center; margin: 20px 0; }
        svg { border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>Exported Cutting Layout</h1>
    <div class="svg-container">
        ${svgString}
    </div>
    <p>Generated on ${new Date().toLocaleString()}</p>
</body>
</html>`;

            // Download as HTML file
            const blob = new Blob([htmlContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const anchor = document.createElement('a');
            anchor.download = 'test-layout.html';
            anchor.href = url;
            anchor.click();
            URL.revokeObjectURL(url);
            
            document.getElementById('status').textContent = 'SVG exported successfully!';
            document.getElementById('status').style.color = 'green';
        });

        // Test print
        document.getElementById('printBtn').addEventListener('click', () => {
            const svgString = exportSVGAsString();
            
            const printWindow = window.open('', '_blank');
            if (printWindow) {
                printWindow.document.write(`
                    <html>
                        <head>
                            <title>Print Test</title>
                            <style>
                                body { margin: 20px; font-family: Arial, sans-serif; }
                                .page { page-break-after: always; text-align: center; }
                                svg { max-width: 100%; height: auto; border: 1px solid #ccc; }
                                h1 { color: #333; }
                            </style>
                        </head>
                        <body>
                            <div class="page">
                                <h1>Test Cutting Layout</h1>
                                ${svgString}
                                <p>Generated on ${new Date().toLocaleString()}</p>
                            </div>
                        </body>
                    </html>
                `);
                printWindow.document.close();
                printWindow.print();
            }
            
            document.getElementById('status').textContent = 'Print dialog opened!';
            document.getElementById('status').style.color = 'blue';
        });

        document.getElementById('status').textContent = 'Ready! Click Export or Print to test.';
        document.getElementById('status').style.color = 'green';
    </script>
</body>
</html>
