/**
 * Optimization Client - Client-side interface for optimization
 * This handles both local optimization (fallback) and server-side optimization
 */

import { 
  OptimizationRequest, 
  OptimizationResponse, 
  ApiClientInterface,
  AppEvents,
  OptimizationError
} from '../types';
import { OptimizationEngine } from './OptimizationEngine';
import { EventEmitter } from '../utils';

export class OptimizationClient extends EventEmitter<AppEvents> {
  private apiClient: ApiClientInterface | null = null;
  private localEngine: OptimizationEngine;
  private enableServerOptimization: boolean = false;

  constructor(apiClient?: ApiClientInterface, enableServerOptimization: boolean = false) {
    super();
    this.apiClient = apiClient || null;
    this.enableServerOptimization = enableServerOptimization;
    this.localEngine = new OptimizationEngine();
  }

  /**
   * Set API client for server-side optimization
   */
  setApiClient(apiClient: ApiClientInterface): void {
    this.apiClient = apiClient;
  }

  /**
   * Enable or disable server-side optimization
   */
  setServerOptimization(enabled: boolean): void {
    this.enableServerOptimization = enabled;
  }

  /**
   * Main optimization method - routes to server or local based on configuration
   */
  async optimize(request: OptimizationRequest): Promise<OptimizationResponse> {
    this.emit('optimization:started', request);

    try {
      let response: OptimizationResponse;

      if (this.enableServerOptimization && this.apiClient) {
        try {
          // Try server-side optimization first
          response = await this.optimizeOnServer(request);
        } catch (error) {
          console.warn('Server optimization failed, falling back to local:', error);
          // Fallback to local optimization
          response = await this.optimizeLocally(request);
        }
      } else {
        // Use local optimization
        response = await this.optimizeLocally(request);
      }

      if (response.success) {
        this.emit('optimization:completed', response);
      } else {
        const error = new OptimizationError(
          response.error || 'Optimization failed',
          'OPTIMIZATION_FAILED',
          response
        );
        this.emit('optimization:failed', error);
      }

      return response;
    } catch (error) {
      const optimizationError = error instanceof OptimizationError 
        ? error 
        : new OptimizationError(
            error instanceof Error ? error.message : 'Unknown optimization error',
            'UNKNOWN_ERROR',
            error
          );

      this.emit('optimization:failed', optimizationError);
      
      return {
        success: false,
        layouts: [],
        error: optimizationError.message,
      };
    }
  }

  /**
   * Optimize using server-side algorithms (protected IP)
   */
  private async optimizeOnServer(request: OptimizationRequest): Promise<OptimizationResponse> {
    if (!this.apiClient) {
      throw new OptimizationError(
        'API client not configured for server optimization',
        'NO_API_CLIENT'
      );
    }

    try {
      const response = await this.apiClient.optimize(request);
      
      if (!response.success) {
        throw new OptimizationError(
          response.error || 'Server optimization failed',
          'SERVER_OPTIMIZATION_FAILED',
          response
        );
      }

      return response;
    } catch (error) {
      if (error instanceof OptimizationError) {
        throw error;
      }
      
      throw new OptimizationError(
        `Server optimization error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'SERVER_ERROR',
        error
      );
    }
  }

  /**
   * Optimize using local algorithms (fallback)
   */
  private async optimizeLocally(request: OptimizationRequest): Promise<OptimizationResponse> {
    try {
      // Check if Packer library is available for local optimization
      if (typeof Packer === 'undefined') {
        throw new OptimizationError(
          'Local optimization library not available. Please check your internet connection or enable server optimization.',
          'NO_LOCAL_LIBRARY'
        );
      }

      return await this.localEngine.optimize(request);
    } catch (error) {
      if (error instanceof OptimizationError) {
        throw error;
      }
      
      throw new OptimizationError(
        `Local optimization error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'LOCAL_ERROR',
        error
      );
    }
  }

  /**
   * Check if server optimization is available
   */
  async isServerOptimizationAvailable(): Promise<boolean> {
    if (!this.apiClient || !this.enableServerOptimization) {
      return false;
    }

    try {
      // This would check server capabilities
      // For now, just return the configuration
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if local optimization is available
   */
  isLocalOptimizationAvailable(): boolean {
    return typeof Packer !== 'undefined';
  }

  /**
   * Get optimization capabilities
   */
  async getCapabilities(): Promise<{
    serverOptimization: boolean;
    localOptimization: boolean;
    advancedAlgorithms: boolean;
    maxPieces: number;
    maxMaterials: number;
  }> {
    const serverAvailable = await this.isServerOptimizationAvailable();
    const localAvailable = this.isLocalOptimizationAvailable();

    return {
      serverOptimization: serverAvailable,
      localOptimization: localAvailable,
      advancedAlgorithms: serverAvailable, // Advanced algorithms only on server
      maxPieces: serverAvailable ? 1000 : 100, // Higher limits on server
      maxMaterials: serverAvailable ? 100 : 20,
    };
  }

  /**
   * Validate optimization request before processing
   */
  private validateRequest(request: OptimizationRequest): void {
    if (!request.materials || request.materials.length === 0) {
      throw new OptimizationError('No materials provided', 'NO_MATERIALS');
    }

    if (!request.pieces || request.pieces.length === 0) {
      throw new OptimizationError('No pieces provided', 'NO_PIECES');
    }

    if (request.sawKerf < 0) {
      throw new OptimizationError('Saw kerf cannot be negative', 'INVALID_KERF');
    }

    // Check limits based on capabilities
    this.getCapabilities().then(capabilities => {
      if (request.pieces.length > capabilities.maxPieces) {
        throw new OptimizationError(
          `Too many pieces. Maximum allowed: ${capabilities.maxPieces}`,
          'TOO_MANY_PIECES'
        );
      }

      if (request.materials.length > capabilities.maxMaterials) {
        throw new OptimizationError(
          `Too many materials. Maximum allowed: ${capabilities.maxMaterials}`,
          'TOO_MANY_MATERIALS'
        );
      }
    });
  }

  /**
   * Get optimization statistics for the current session
   */
  getOptimizationStats(): {
    totalOptimizations: number;
    serverOptimizations: number;
    localOptimizations: number;
    averageProcessingTime: number;
    successRate: number;
  } {
    // This would track statistics in a real implementation
    return {
      totalOptimizations: 0,
      serverOptimizations: 0,
      localOptimizations: 0,
      averageProcessingTime: 0,
      successRate: 100,
    };
  }

  /**
   * Clear optimization cache (if any)
   */
  clearCache(): void {
    // This would clear any cached optimization results
    console.log('Optimization cache cleared');
  }

  /**
   * Set optimization preferences
   */
  setPreferences(preferences: {
    preferServerOptimization?: boolean;
    maxProcessingTime?: number;
    enableCaching?: boolean;
    optimizationStrategy?: 'speed' | 'quality' | 'balanced';
  }): void {
    if (preferences.preferServerOptimization !== undefined) {
      this.enableServerOptimization = preferences.preferServerOptimization;
    }

    // Other preferences would be stored and used during optimization
    console.log('Optimization preferences updated:', preferences);
  }
}
