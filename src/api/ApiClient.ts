/**
 * API Client - Handles communication with the server
 * Provides authentication and optimization endpoints
 */

import {
  ApiClientInterface,
  AuthCredentials,
  AuthResponse,
  OptimizationRequest,
  OptimizationResponse,
  Project,
  ApiError,
} from "../types";

export class ApiClient implements ApiClientInterface {
  private baseUrl: string;
  private authToken: string | null = null;
  private refreshTokenValue: string | null = null;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl.replace(/\/$/, ""); // Remove trailing slash
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string | null): void {
    this.authToken = token;
  }

  /**
   * Get current authentication token
   */
  getAuthToken(): string | null {
    return this.authToken;
  }

  /**
   * Make authenticated HTTP request
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers: HeadersInit = {
      "Content-Type": "application/json",
      ...options.headers,
    };

    if (this.authToken) {
      headers["Authorization"] = `Bearer ${this.authToken}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      // Handle authentication errors
      if (response.status === 401) {
        // Try to refresh token
        if (this.refreshTokenValue) {
          try {
            await this.refreshTokenInternal();
            // Retry the original request with new token
            headers["Authorization"] = `Bearer ${this.authToken}`;
            const retryResponse = await fetch(url, {
              ...options,
              headers,
            });

            if (!retryResponse.ok) {
              throw new ApiError(
                "Authentication failed after token refresh",
                retryResponse.status,
                "AUTH_FAILED",
              );
            }

            return await retryResponse.json();
          } catch (refreshError) {
            // Refresh failed, clear tokens and throw auth error
            this.authToken = null;
            this.refreshTokenValue = null;
            throw new ApiError(
              "Authentication expired. Please log in again.",
              401,
              "TOKEN_EXPIRED",
            );
          }
        } else {
          throw new ApiError("Authentication required", 401, "AUTH_REQUIRED");
        }
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(
          errorData.message ||
            `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorData.code,
          errorData,
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      // Network or other errors
      throw new ApiError(
        `Network error: ${error instanceof Error ? error.message : "Unknown error"}`,
        0,
        "NETWORK_ERROR",
        error,
      );
    }
  }

  /**
   * Authenticate with credentials
   */
  async authenticate(credentials: AuthCredentials): Promise<AuthResponse> {
    try {
      const response = await this.makeRequest<AuthResponse>("/api/auth/login", {
        method: "POST",
        body: JSON.stringify(credentials),
      });

      if (response.success && response.token) {
        this.authToken = response.token;
        this.refreshTokenValue = response.refreshToken || null;
      }

      return response;
    } catch (error) {
      if (error instanceof ApiError) {
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: false,
        error: "Authentication failed",
      };
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<AuthResponse> {
    return this.refreshTokenInternal();
  }

  /**
   * Internal token refresh method
   */
  private async refreshTokenInternal(): Promise<AuthResponse> {
    if (!this.refreshTokenValue) {
      throw new ApiError("No refresh token available", 401, "NO_REFRESH_TOKEN");
    }

    try {
      const response = await this.makeRequest<AuthResponse>(
        "/api/auth/refresh",
        {
          method: "POST",
          body: JSON.stringify({ refreshToken: this.refreshTokenValue }),
        },
      );

      if (response.success && response.token) {
        this.authToken = response.token;
        this.refreshTokenValue = response.refreshToken || this.refreshTokenValue;
      }

      return response;
    } catch (error) {
      // Clear tokens on refresh failure
      this.authToken = null;
      this.refreshTokenValue = null;
      throw error;
    }
  }

  /**
   * Logout and clear tokens
   */
  async logout(): Promise<void> {
    try {
      if (this.authToken) {
        await this.makeRequest("/api/auth/logout", {
          method: "POST",
        });
      }
    } catch (error) {
      // Ignore logout errors, just clear tokens
      console.warn("Logout request failed:", error);
    } finally {
      this.authToken = null;
      this.refreshTokenValue = null;
    }
  }

  /**
   * Optimize using server-side algorithms
   */
  async optimize(request: OptimizationRequest): Promise<OptimizationResponse> {
    try {
      const response = await this.makeRequest<OptimizationResponse>(
        "/api/optimize",
        {
          method: "POST",
          body: JSON.stringify(request),
        },
      );

      return response;
    } catch (error) {
      if (error instanceof ApiError) {
        return {
          success: false,
          layouts: [],
          error: error.message,
        };
      }

      return {
        success: false,
        layouts: [],
        error: "Optimization request failed",
      };
    }
  }

  /**
   * Sync project with server
   */
  async syncProject(project: Project): Promise<Project> {
    const response = await this.makeRequest<{ project: Project }>(
      "/api/projects/sync",
      {
        method: "POST",
        body: JSON.stringify({ project }),
      },
    );

    return response.project;
  }

  /**
   * Get shared projects
   */
  async getSharedProjects(): Promise<Project[]> {
    const response = await this.makeRequest<{ projects: Project[] }>(
      "/api/projects/shared",
    );
    return response.projects || [];
  }

  /**
   * Check server health
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.makeRequest("/api/health");
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get server capabilities
   */
  async getCapabilities(): Promise<{
    optimization: boolean;
    sharing: boolean;
    authentication: boolean;
    maxProjects: number;
    maxMaterials: number;
    maxPieces: number;
    supportedFormats: string[];
  }> {
    try {
      const response = await this.makeRequest<any>("/api/capabilities");
      return {
        optimization: response.optimization || false,
        sharing: response.sharing || false,
        authentication: response.authentication || false,
        maxProjects: response.maxProjects || 10,
        maxMaterials: response.maxMaterials || 100,
        maxPieces: response.maxPieces || 1000,
        supportedFormats: response.supportedFormats || ["json"],
      };
    } catch {
      return {
        optimization: false,
        sharing: false,
        authentication: false,
        maxProjects: 10,
        maxMaterials: 100,
        maxPieces: 1000,
        supportedFormats: ["json"],
      };
    }
  }

  /**
   * Upload project file
   */
  async uploadProject(file: File): Promise<Project> {
    const formData = new FormData();
    formData.append("project", file);

    const response = await fetch(`${this.baseUrl}/api/projects/upload`, {
      method: "POST",
      headers: {
        Authorization: this.authToken ? `Bearer ${this.authToken}` : "",
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new ApiError(
        errorData.message || "Upload failed",
        response.status,
        errorData.code,
      );
    }

    const result = await response.json();
    return result.project;
  }

  /**
   * Download project as file
   */
  async downloadProject(
    projectId: string,
    format: "json" | "csv" | "pdf" = "json",
  ): Promise<Blob> {
    const response = await fetch(
      `${this.baseUrl}/api/projects/${projectId}/download?format=${format}`,
      {
        headers: {
          Authorization: this.authToken ? `Bearer ${this.authToken}` : "",
        },
      },
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new ApiError(
        errorData.message || "Download failed",
        response.status,
        errorData.code,
      );
    }

    return await response.blob();
  }

  /**
   * Get user profile
   */
  async getUserProfile(): Promise<any> {
    const response = await this.makeRequest<any>("/api/user/profile");
    return response.user;
  }

  /**
   * Update user profile
   */
  async updateUserProfile(updates: any): Promise<any> {
    const response = await this.makeRequest<any>("/api/user/profile", {
      method: "PUT",
      body: JSON.stringify(updates),
    });
    return response.user;
  }

  /**
   * Get usage statistics
   */
  async getUsageStats(): Promise<{
    optimizationsThisMonth: number;
    projectsCreated: number;
    storageUsed: number;
    planLimits: {
      maxOptimizations: number;
      maxProjects: number;
      maxStorage: number;
    };
  }> {
    try {
      const response = await this.makeRequest<any>("/api/user/usage");
      return response.stats;
    } catch {
      return {
        optimizationsThisMonth: 0,
        projectsCreated: 0,
        storageUsed: 0,
        planLimits: {
          maxOptimizations: 100,
          maxProjects: 10,
          maxStorage: 100 * 1024 * 1024, // 100MB
        },
      };
    }
  }
}
