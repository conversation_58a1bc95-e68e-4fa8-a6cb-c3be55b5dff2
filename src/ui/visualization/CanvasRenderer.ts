/**
 * <PERSON><PERSON> Renderer - <PERSON><PERSON> visualization of cutting layouts (LEGACY - replaced by D3Canvas<PERSON><PERSON><PERSON>)
 * This file is kept for reference but is no longer used in the application
 */

import { OptimizedSheetLayout, VisualizationProps, Unit } from '../../types';
import { convertToUnit, formatNumber } from '../../utils';

export class CanvasRenderer {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private props: VisualizationProps;
  private baseUnit: Unit = 'mm';

  // Navigation elements
  private prevButton: HTMLButtonElement;
  private nextButton: HTMLButtonElement;
  private sheetIndicator: HTMLSpanElement;

  // Interactive state
  private selectedPiece: any | null = null;
  private hoveredPiece: any | null = null;
  private pieceInfoPanel: HTMLDivElement | null = null;

  constructor(
    canvas: HTMLCanvasElement,
    props: VisualizationProps,
    navigationElements: {
      prevButton: HTMLButtonElement;
      nextButton: HTMLButtonElement;
      sheetIndicator: HTMLSpanElement;
    }
  ) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d')!;
    this.props = props;
    this.prevButton = navigationElements.prevButton;
    this.nextButton = navigationElements.nextButton;
    this.sheetIndicator = navigationElements.sheetIndicator;

    this.createPieceInfoPanel();
    this.bindEvents();
    this.render();
  }

  /**
   * Update props and re-render
   */
  updateProps(newProps: VisualizationProps): void {
    this.props = newProps;
    this.selectedPiece = null; // Reset selection when props change
    this.hoveredPiece = null;
    this.updatePieceInfoPanel(null);
    this.render();
  }

  /**
   * Create piece information panel
   */
  private createPieceInfoPanel(): void {
    this.pieceInfoPanel = document.createElement('div');
    this.pieceInfoPanel.className = 'piece-info-panel';
    this.pieceInfoPanel.style.cssText = `
      position: absolute;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 12px;
      border-radius: 6px;
      font-size: 12px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      pointer-events: none;
      z-index: 1000;
      display: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      max-width: 200px;
      line-height: 1.4;
      top: 10px;
      right: 10px;
      transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
      transform: translateY(0);
      opacity: 0;
    `;

    // Insert into canvas container
    const canvasContainer = this.canvas.parentElement;
    if (canvasContainer) {
      canvasContainer.appendChild(this.pieceInfoPanel);
    }
  }

  /**
   * Bind navigation events
   */
  private bindEvents(): void {
    this.prevButton.addEventListener('click', () => {
      if (this.props.currentSheetIndex > 0) {
        this.props.onSheetChange(this.props.currentSheetIndex - 1);
      }
    });

    this.nextButton.addEventListener('click', () => {
      if (this.props.currentSheetIndex < this.props.layouts.length - 1) {
        this.props.onSheetChange(this.props.currentSheetIndex + 1);
      }
    });

    // Handle canvas interactions
    this.canvas.addEventListener('click', (e) => this.handleCanvasClick(e));
    this.canvas.addEventListener('mousemove', (e) => this.handleCanvasMouseMove(e));
    this.canvas.addEventListener('mouseleave', () => this.handleCanvasMouseLeave());

    // Set canvas cursor style
    this.canvas.style.cursor = 'pointer';
  }

  /**
   * Main render method
   */
  private render(): void {
    this.updateNavigation();

    if (this.props.layouts.length === 0) {
      this.renderEmptyState();
      return;
    }

    const layout = this.props.layouts[this.props.currentSheetIndex];
    if (!layout) {
      this.renderEmptyState();
      return;
    }

    this.renderLayout(layout);
  }

  /**
   * Render empty state when no layouts available
   */
  private renderEmptyState(): void {
    const canvasWidth = this.canvas.clientWidth || 300;
    this.canvas.width = canvasWidth;
    this.canvas.height = canvasWidth * (9 / 16);

    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    this.ctx.font = "14px Arial";
    this.ctx.textAlign = "center";
    this.ctx.fillStyle = "#777";
    this.ctx.fillText(
      "No layout to display. Add items and optimize.",
      this.canvas.width / 2,
      this.canvas.height / 2
    );
  }

  /**
   * Render a specific layout
   */
  private renderLayout(layout: OptimizedSheetLayout): void {
    // Set canvas dimensions based on layout aspect ratio
    const canvasClientWidth = this.canvas.clientWidth;
    const sheetAspectRatio = layout.heightUsed / layout.widthUsed;
    this.canvas.width = canvasClientWidth;
    this.canvas.height = canvasClientWidth * sheetAspectRatio;

    // Calculate scaling
    const scaleX = this.canvas.width / layout.widthUsed;
    const scaleY = this.canvas.height / layout.heightUsed;
    const scale = Math.min(scaleX, scaleY) * 0.98; // 98% to leave some margin

    const drawingWidth = layout.widthUsed * scale;
    const drawingHeight = layout.heightUsed * scale;
    const offsetX = (this.canvas.width - drawingWidth) / 2;
    const offsetY = (this.canvas.height - drawingHeight) / 2;

    // Clear canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // Draw material sheet outline
    this.drawMaterialOutline(offsetX, offsetY, drawingWidth, drawingHeight);

    // Draw dimension labels
    this.drawDimensionLabels(layout, offsetX, offsetY, drawingWidth, drawingHeight);

    // Draw pieces
    layout.pieces.forEach(piece => {
      const isSelected = this.selectedPiece && this.selectedPiece.id === piece.id;
      const isHovered = this.hoveredPiece && this.hoveredPiece.id === piece.id;
      this.drawPiece(piece, scale, offsetX, offsetY, isSelected, isHovered);
    });
  }

  /**
   * Draw material sheet outline
   */
  private drawMaterialOutline(x: number, y: number, width: number, height: number): void {
    this.ctx.strokeStyle = "#333";
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(x, y, width, height);

    // Add a subtle background
    this.ctx.fillStyle = "#fafafa";
    this.ctx.fillRect(x, y, width, height);
  }

  /**
   * Draw dimension labels
   */
  private drawDimensionLabels(
    layout: OptimizedSheetLayout,
    offsetX: number,
    offsetY: number,
    drawingWidth: number,
    drawingHeight: number
  ): void {
    this.ctx.fillStyle = "#555";
    this.ctx.font = "10px Arial";
    this.ctx.textAlign = "center";

    // Top label: Material Length (vertical dimension in the layout)
    const lengthLabel = `Length: ${layout.baseMaterial.length}${layout.baseMaterial.unit} (${formatNumber(layout.heightUsed)}${this.baseUnit})`;
    this.ctx.fillText(
      lengthLabel,
      offsetX + drawingWidth / 2,
      offsetY - 5 > 10 ? offsetY - 5 : 12
    );

    // Side label: Material Width (horizontal dimension in the layout)
    this.ctx.save();
    this.ctx.translate(
      offsetX - 5 < 0 ? 12 : offsetX - 5,
      offsetY + drawingHeight / 2
    );
    this.ctx.rotate(-Math.PI / 2);
    this.ctx.textAlign = "center";
    const widthLabel = `Width: ${layout.baseMaterial.width}${layout.baseMaterial.unit} (${formatNumber(layout.widthUsed)}${this.baseUnit})`;
    this.ctx.fillText(widthLabel, 0, 0);
    this.ctx.restore();
  }

  /**
   * Draw a single piece
   */
  private drawPiece(
    piece: any,
    scale: number,
    offsetX: number,
    offsetY: number,
    isSelected: boolean = false,
    isHovered: boolean = false
  ): void {
    const x = piece.x * scale + offsetX;
    const y = piece.y * scale + offsetY;
    
    // Calculate actual piece dimensions (without kerf)
    const pieceActualWidth = convertToUnit(piece.width, piece.unit, this.baseUnit) * scale;
    const pieceActualHeight = convertToUnit(piece.length, piece.unit, this.baseUnit) * scale;
    
    // Packed dimensions (with kerf)
    const packedPieceWidth = piece.packedWidth * scale;
    const packedPieceHeight = piece.packedHeight * scale;

    // Draw kerf area (lighter background)
    this.ctx.fillStyle = piece.color + "30"; // 30% opacity
    this.ctx.fillRect(x, y, packedPieceWidth, packedPieceHeight);

    // Draw kerf outline
    this.ctx.strokeStyle = piece.color + "60"; // 60% opacity
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(x, y, packedPieceWidth, packedPieceHeight);

    // Calculate piece position within kerf area (centered)
    const pieceXDisplayOffset = (packedPieceWidth - pieceActualWidth) / 2;
    const pieceYDisplayOffset = (packedPieceHeight - pieceActualHeight) / 2;

    const actualPieceX = x + pieceXDisplayOffset;
    const actualPieceY = y + pieceYDisplayOffset;

    // Add shadow for depth
    const shadowOffset = isSelected ? 4 : isHovered ? 3 : 2;
    const shadowOpacity = isSelected ? 0.3 : isHovered ? 0.2 : 0.1;
    this.ctx.fillStyle = `rgba(0, 0, 0, ${shadowOpacity})`;
    this.ctx.fillRect(
      actualPieceX + shadowOffset,
      actualPieceY + shadowOffset,
      pieceActualWidth,
      pieceActualHeight
    );

    // Draw actual piece with enhanced colors and gradient for 3D effect
    let pieceColor = piece.color;
    if (isSelected) {
      // Brighten the color for selected pieces
      pieceColor = this.adjustColorBrightness(piece.color, 20);
    } else if (isHovered) {
      // Slightly brighten for hovered pieces
      pieceColor = this.adjustColorBrightness(piece.color, 10);
    }

    // Create gradient for 3D effect
    const gradient = this.ctx.createLinearGradient(
      actualPieceX, actualPieceY,
      actualPieceX + pieceActualWidth, actualPieceY + pieceActualHeight
    );
    gradient.addColorStop(0, this.adjustColorBrightness(pieceColor, 15));
    gradient.addColorStop(0.5, pieceColor);
    gradient.addColorStop(1, this.adjustColorBrightness(pieceColor, -15));

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(actualPieceX, actualPieceY, pieceActualWidth, pieceActualHeight);

    // Draw piece outline with enhanced styling for interaction states
    if (isSelected) {
      // Draw thick selection border
      this.ctx.strokeStyle = "#FFD700"; // Gold color for selection
      this.ctx.lineWidth = 3;
      this.ctx.strokeRect(actualPieceX - 1, actualPieceY - 1, pieceActualWidth + 2, pieceActualHeight + 2);

      // Add inner border
      this.ctx.strokeStyle = "#333";
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(actualPieceX, actualPieceY, pieceActualWidth, pieceActualHeight);
    } else if (isHovered) {
      // Draw hover border
      this.ctx.strokeStyle = "#4A90E2"; // Blue color for hover
      this.ctx.lineWidth = 2;
      this.ctx.strokeRect(actualPieceX, actualPieceY, pieceActualWidth, pieceActualHeight);
    } else {
      // Normal border
      this.ctx.strokeStyle = "#333";
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(actualPieceX, actualPieceY, pieceActualWidth, pieceActualHeight);
    }

    // Draw piece label
    this.drawPieceLabel(
      piece,
      actualPieceX,
      actualPieceY,
      pieceActualWidth,
      pieceActualHeight,
      isSelected || isHovered
    );
  }

  /**
   * Draw piece label with name and dimensions
   */
  private drawPieceLabel(
    piece: any,
    x: number,
    y: number,
    width: number,
    height: number,
    isHighlighted: boolean = false
  ): void {
    // Use contrasting text color based on highlight state
    this.ctx.fillStyle = isHighlighted ? "#000" : "#fff";
    this.ctx.textAlign = "center";

    const textX = x + width / 2;
    const textY = y + height / 2;

    const pieceLabel = piece.name;
    const pieceDims = `${piece.length}${piece.unit} × ${piece.width}${piece.unit}`;

    // Add text shadow for better readability when highlighted
    if (isHighlighted) {
      this.ctx.strokeStyle = "#fff";
      this.ctx.lineWidth = 2;
    }

    // Adjust font size and content based on piece size
    if (height > 25 && width > 50) {
      this.ctx.font = "bold 10px Arial";
      if (isHighlighted) {
        this.ctx.strokeText(pieceLabel, textX, textY - 5);
      }
      this.ctx.fillText(pieceLabel, textX, textY - 5);

      this.ctx.font = "8px Arial";
      if (isHighlighted) {
        this.ctx.strokeText(pieceDims, textX, textY + 8);
      }
      this.ctx.fillText(pieceDims, textX, textY + 8);
    } else if (height > 10 && width > 30) {
      this.ctx.font = "bold 9px Arial";
      if (isHighlighted) {
        this.ctx.strokeText(pieceLabel, textX, textY);
      }
      this.ctx.fillText(pieceLabel, textX, textY);
    } else if (height > 10 && width > 10) {
      this.ctx.font = "bold 7px Arial";
      if (isHighlighted) {
        this.ctx.strokeText(pieceLabel, textX, textY);
      }
      this.ctx.fillText(pieceLabel, textX, textY);
    }
    // Don't draw text for very small pieces
  }

  /**
   * Helper method to adjust color brightness
   */
  private adjustColorBrightness(color: string, percent: number): string {
    // Simple brightness adjustment for hex colors
    const num = parseInt(color.replace("#", ""), 16);
    const amt = Math.round(2.55 * percent);
    const R = Math.max(0, Math.min(255, (num >> 16) + amt));
    const G = Math.max(0, Math.min(255, (num >> 8 & 0x00FF) + amt));
    const B = Math.max(0, Math.min(255, (num & 0x0000FF) + amt));
    return "#" + ((1 << 24) + (R << 16) + (G << 8) + B).toString(16).slice(1);
  }

  /**
   * Update navigation buttons and indicator
   */
  private updateNavigation(): void {
    const totalSheets = this.props.layouts.length;
    const currentSheet = totalSheets > 0 ? this.props.currentSheetIndex + 1 : 0;

    this.sheetIndicator.textContent = `Sheet ${currentSheet} of ${totalSheets}`;
    this.prevButton.disabled = this.props.currentSheetIndex === 0;
    this.nextButton.disabled = this.props.currentSheetIndex >= totalSheets - 1;
  }

  /**
   * Handle canvas click for piece selection
   */
  private handleCanvasClick(event: MouseEvent): void {
    if (this.props.layouts.length === 0) return;

    const layout = this.props.layouts[this.props.currentSheetIndex];
    if (!layout) return;

    const clickedPiece = this.getPieceAtPosition(event);

    if (clickedPiece) {
      // Toggle selection
      if (this.selectedPiece && this.selectedPiece.id === clickedPiece.id) {
        this.selectedPiece = null;
        this.updatePieceInfoPanel(null);
      } else {
        this.selectedPiece = clickedPiece;
        this.updatePieceInfoPanel(clickedPiece);
      }

      // Notify parent component
      if (this.props.onPieceSelect) {
        this.props.onPieceSelect(this.selectedPiece);
      }

      this.render();
    } else {
      // Clicked on empty space - deselect
      if (this.selectedPiece) {
        this.selectedPiece = null;
        this.updatePieceInfoPanel(null);
        if (this.props.onPieceSelect) {
          this.props.onPieceSelect(null);
        }
        this.render();
      }
    }
  }

  /**
   * Handle canvas mouse move for hover effects
   */
  private handleCanvasMouseMove(event: MouseEvent): void {
    if (this.props.layouts.length === 0) return;

    const hoveredPiece = this.getPieceAtPosition(event);

    if (hoveredPiece !== this.hoveredPiece) {
      this.hoveredPiece = hoveredPiece;
      this.render();

      // Update cursor
      this.canvas.style.cursor = hoveredPiece ? 'pointer' : 'default';

      // Show piece info on hover if no piece is selected
      if (!this.selectedPiece && hoveredPiece) {
        this.updatePieceInfoPanel(hoveredPiece);
      } else if (!this.selectedPiece && !hoveredPiece) {
        this.updatePieceInfoPanel(null);
      }
    }
  }

  /**
   * Handle canvas mouse leave
   */
  private handleCanvasMouseLeave(): void {
    if (this.hoveredPiece) {
      this.hoveredPiece = null;
      this.render();
    }
    this.canvas.style.cursor = 'default';
  }

  /**
   * Get piece at mouse position
   */
  private getPieceAtPosition(event: MouseEvent): any | null {
    const layout = this.props.layouts[this.props.currentSheetIndex];
    if (!layout) return null;

    const rect = this.canvas.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const clickY = event.clientY - rect.top;

    // Convert click coordinates to layout coordinates
    const scaleX = this.canvas.width / layout.widthUsed;
    const scaleY = this.canvas.height / layout.heightUsed;
    const scale = Math.min(scaleX, scaleY) * 0.98;

    const drawingWidth = layout.widthUsed * scale;
    const drawingHeight = layout.heightUsed * scale;
    const offsetX = (this.canvas.width - drawingWidth) / 2;
    const offsetY = (this.canvas.height - drawingHeight) / 2;

    const layoutX = (clickX - offsetX) / scale;
    const layoutY = (clickY - offsetY) / scale;

    // Find piece at position
    return layout.pieces.find(piece => {
      return layoutX >= piece.x &&
             layoutX <= piece.x + piece.packedWidth &&
             layoutY >= piece.y &&
             layoutY <= piece.y + piece.packedHeight;
    }) || null;
  }

  /**
   * Update piece information panel
   */
  private updatePieceInfoPanel(piece: any | null): void {
    if (!this.pieceInfoPanel) return;

    if (piece) {
      const grainText = piece.grainDirection !== 'none'
        ? `<br><strong>Grain:</strong> ${piece.grainDirection === 'length' ? 'Along Length' : 'Along Width'}`
        : '';

      this.pieceInfoPanel.innerHTML = `
        <div style="margin-bottom: 4px;"><strong>${piece.name}</strong></div>
        <div style="font-size: 11px; color: #ccc;">
          <strong>Dimensions:</strong> ${piece.length}${piece.unit} × ${piece.width}${piece.unit}${grainText}
        </div>
      `;
      this.pieceInfoPanel.style.display = 'block';
      // Trigger animation
      setTimeout(() => {
        if (this.pieceInfoPanel) {
          this.pieceInfoPanel.style.opacity = '1';
          this.pieceInfoPanel.style.transform = 'translateY(0)';
        }
      }, 10);
    } else {
      if (this.pieceInfoPanel) {
        this.pieceInfoPanel.style.opacity = '0';
        this.pieceInfoPanel.style.transform = 'translateY(-10px)';
        setTimeout(() => {
          if (this.pieceInfoPanel) {
            this.pieceInfoPanel.style.display = 'none';
          }
        }, 200);
      }
    }
  }

  /**
   * Get currently selected piece
   */
  getSelectedPiece(): any | null {
    return this.selectedPiece;
  }

  /**
   * Set selected piece programmatically
   */
  setSelectedPiece(piece: any | null): void {
    this.selectedPiece = piece;
    this.updatePieceInfoPanel(piece);
    if (this.props.onPieceSelect) {
      this.props.onPieceSelect(piece);
    }
    this.render();
  }

  /**
   * Export canvas as image
   */
  exportAsImage(format: 'png' | 'jpeg' = 'png'): string {
    return this.canvas.toDataURL(`image/${format}`);
  }

  /**
   * Get canvas dimensions
   */
  getCanvasDimensions(): { width: number; height: number } {
    return {
      width: this.canvas.width,
      height: this.canvas.height,
    };
  }

  /**
   * Set canvas size
   */
  setCanvasSize(width: number, height: number): void {
    this.canvas.width = width;
    this.canvas.height = height;
    this.render();
  }

  /**
   * Destroy the component
   */
  destroy(): void {
    // Remove event listeners
    this.prevButton.removeEventListener('click', () => {});
    this.nextButton.removeEventListener('click', () => {});
    this.canvas.removeEventListener('click', () => {});
    this.canvas.removeEventListener('mousemove', () => {});
    this.canvas.removeEventListener('mouseleave', () => {});

    // Remove piece info panel
    if (this.pieceInfoPanel && this.pieceInfoPanel.parentNode) {
      this.pieceInfoPanel.parentNode.removeChild(this.pieceInfoPanel);
    }
  }
}
