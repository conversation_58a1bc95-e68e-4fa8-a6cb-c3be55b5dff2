/**
 * Inventory Management View
 * Dedicated view for managing base materials inventory
 * Separated from project management for better organization
 */

import { BaseMaterial } from '../../types';
import { MaterialForm } from '../components/MaterialForm';
import { InventoryList } from '../components/InventoryList';

export interface InventoryViewProps {
  materials: BaseMaterial[];
  onMaterialAdd: (material: Omit<BaseMaterial, 'id'>) => void;
  onMaterialUpdate: (id: string, material: Omit<BaseMaterial, 'id'>) => void;
  onMaterialDelete: (id: string) => void;
  onMaterialDuplicate: (id: string) => void;
  onImportMaterials?: () => void;
  onExportMaterials?: () => void;
}

export class InventoryView {
  private container: HTMLElement;
  private props: InventoryViewProps;
  private materialForm: MaterialForm | null = null;
  private inventoryList: InventoryList | null = null;
  private editingMaterial: BaseMaterial | null = null;

  constructor(container: HTMLElement, props: InventoryViewProps) {
    this.container = container;
    this.props = props;
    this.createView();
    this.initializeComponents();
  }

  /**
   * Update component props and re-render
   */
  updateProps(newProps: InventoryViewProps): void {
    this.props = newProps;
    this.updateComponents();
  }

  /**
   * Create the inventory view structure
   */
  private createView(): void {
    this.container.innerHTML = `
      <div class="inventory-view">
        <header class="inventory-header">
          <div class="header-content">
            <h1><span aria-hidden="true">📦</span> Base Materials Inventory</h1>
            <p class="header-description">
              Manage your base materials that will be used for cutting pieces. 
              Add sheets, boards, and other materials with their dimensions.
            </p>
          </div>
          <div class="header-actions">
            <div class="material-count">
              <span class="count-number">${this.props.materials.length}</span>
              <span class="count-label">materials</span>
            </div>
            ${this.props.onImportMaterials ? `
              <button class="import-btn" aria-label="Import materials">
                Import
              </button>
            ` : ''}
            ${this.props.onExportMaterials ? `
              <button class="export-btn" aria-label="Export materials">
                Export
              </button>
            ` : ''}
          </div>
        </header>

        <div class="inventory-content">
          <div class="material-form-section">
            <div class="section-header">
              <h2>Add New Material</h2>
              <p>Enter the dimensions and details for a new base material.</p>
            </div>
            <div class="material-form-container">
              <!-- Material form will be rendered here -->
            </div>
          </div>

          <div class="inventory-list-section">
            <div class="section-header">
              <h2>Current Inventory</h2>
              <p>Your available base materials for cutting projects.</p>
            </div>
            <div class="inventory-list-container">
              <!-- Inventory list will be rendered here -->
            </div>
          </div>
        </div>
      </div>
    `;

    this.bindHeaderEvents();
  }

  /**
   * Bind header event listeners
   */
  private bindHeaderEvents(): void {
    const importBtn = this.container.querySelector('.import-btn');
    const exportBtn = this.container.querySelector('.export-btn');

    importBtn?.addEventListener('click', () => {
      if (this.props.onImportMaterials) {
        this.props.onImportMaterials();
      }
    });

    exportBtn?.addEventListener('click', () => {
      if (this.props.onExportMaterials) {
        this.props.onExportMaterials();
      }
    });
  }

  /**
   * Initialize form and list components
   */
  private initializeComponents(): void {
    this.initializeMaterialForm();
    this.initializeInventoryList();
  }

  /**
   * Initialize material form component
   */
  private initializeMaterialForm(): void {
    const materialFormContainer = this.container.querySelector('.material-form-container');
    if (!materialFormContainer) return;

    this.materialForm = new MaterialForm(materialFormContainer as HTMLElement, {
      onAdd: (material) => this.handleAddMaterial(material),
      onUpdate: (id, material) => this.handleUpdateMaterial(id, material),
      onCancelEdit: () => this.handleCancelEdit(),
    });
  }

  /**
   * Initialize inventory list component
   */
  private initializeInventoryList(): void {
    const inventoryListContainer = this.container.querySelector('.inventory-list-container');
    if (!inventoryListContainer) return;

    this.inventoryList = new InventoryList(inventoryListContainer as HTMLElement, {
      materials: this.props.materials,
      onEdit: (material) => this.handleEditMaterial(material),
      onDelete: (id) => this.handleDeleteMaterial(id),
      onDuplicate: (id) => this.handleDuplicateMaterial(id),
    });
  }

  /**
   * Handle adding new material
   */
  private handleAddMaterial(material: Omit<BaseMaterial, 'id'>): void {
    this.props.onMaterialAdd(material);
    this.updateMaterialCount();
  }

  /**
   * Handle updating existing material
   */
  private handleUpdateMaterial(id: string, material: Omit<BaseMaterial, 'id'>): void {
    this.props.onMaterialUpdate(id, material);
    this.editingMaterial = null;
  }

  /**
   * Handle editing material
   */
  private handleEditMaterial(material: BaseMaterial): void {
    this.editingMaterial = material;
    
    if (this.materialForm) {
      this.materialForm.updateProps({
        onAdd: (m) => this.handleAddMaterial(m),
        onUpdate: (id, m) => this.handleUpdateMaterial(id, m),
        editingMaterial: material,
        onCancelEdit: () => this.handleCancelEdit(),
      });
    }

    // Scroll to form
    const formSection = this.container.querySelector('.material-form-section');
    if (formSection) {
      formSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  /**
   * Handle deleting material
   */
  private handleDeleteMaterial(id: string): void {
    // Show confirmation dialog
    const material = this.props.materials.find(m => m.id === id);
    if (!material) return;

    const confirmed = confirm(
      `Are you sure you want to delete "${material.name}"?\n\n` +
      `This will remove the material from your inventory. This action cannot be undone.`
    );

    if (confirmed) {
      this.props.onMaterialDelete(id);
      this.updateMaterialCount();
    }
  }

  /**
   * Handle duplicating material
   */
  private handleDuplicateMaterial(id: string): void {
    this.props.onMaterialDuplicate(id);
    this.updateMaterialCount();
  }

  /**
   * Handle cancel edit
   */
  private handleCancelEdit(): void {
    this.editingMaterial = null;
    
    if (this.materialForm) {
      this.materialForm.updateProps({
        onAdd: (m) => this.handleAddMaterial(m),
        onUpdate: (id, m) => this.handleUpdateMaterial(id, m),
        editingMaterial: undefined,
        onCancelEdit: () => this.handleCancelEdit(),
      });
    }
  }

  /**
   * Update all components with new props
   */
  private updateComponents(): void {
    this.updateMaterialCount();

    // Update inventory list
    if (this.inventoryList) {
      this.inventoryList.updateProps({
        materials: this.props.materials,
        onEdit: (material) => this.handleEditMaterial(material),
        onDelete: (id) => this.handleDeleteMaterial(id),
        onDuplicate: (id) => this.handleDuplicateMaterial(id),
      });
    }

    // If we're editing a material that no longer exists, cancel edit
    if (this.editingMaterial && !this.props.materials.find(m => m.id === this.editingMaterial!.id)) {
      this.handleCancelEdit();
    }
  }

  /**
   * Update material count in header
   */
  private updateMaterialCount(): void {
    const countNumber = this.container.querySelector('.count-number');
    const countLabel = this.container.querySelector('.count-label');
    
    if (countNumber) {
      countNumber.textContent = this.props.materials.length.toString();
    }
    
    if (countLabel) {
      countLabel.textContent = this.props.materials.length === 1 ? 'material' : 'materials';
    }
  }

  /**
   * Show empty state when no materials
   */
  private showEmptyState(): void {
    const listContainer = this.container.querySelector('.inventory-list-container');
    if (!listContainer) return;

    listContainer.innerHTML = `
      <div class="empty-state">
        <div class="empty-icon">📦</div>
        <h3>No materials in inventory</h3>
        <p>Add your first base material to get started with your woodworking projects.</p>
        <button class="add-first-material-btn">Add Your First Material</button>
      </div>
    `;

    const addFirstBtn = listContainer.querySelector('.add-first-material-btn');
    addFirstBtn?.addEventListener('click', () => {
      const formSection = this.container.querySelector('.material-form-section');
      if (formSection) {
        formSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    });
  }

  /**
   * Get current editing material
   */
  getEditingMaterial(): BaseMaterial | null {
    return this.editingMaterial;
  }

  /**
   * Clear any editing state
   */
  clearEditingState(): void {
    this.handleCancelEdit();
  }

  /**
   * Destroy the component
   */
  destroy(): void {
    if (this.materialForm) {
      this.materialForm.destroy();
      this.materialForm = null;
    }

    if (this.inventoryList) {
      this.inventoryList.destroy();
      this.inventoryList = null;
    }

    this.container.innerHTML = '';
  }
}
