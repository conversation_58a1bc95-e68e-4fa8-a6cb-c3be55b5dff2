/**
 * Project Detail View
 * Dedicated view for working on a specific project
 * Contains tabbed interface for Cutting List and Visualization
 */

import { Project, ProjectPiece, BaseMaterial, OptimizedSheetLayout, TabDefinition } from '../../types';
import { TabContainer } from '../components/TabContainer';
import { PieceForm } from '../components/PieceForm';
import { PieceList } from '../components/PieceList';

export interface ProjectDetailViewProps {
  project: Project;
  materials: BaseMaterial[];
  optimizedLayouts: OptimizedSheetLayout[];
  currentSheetIndex: number;
  onPieceAdd: (piece: Omit<ProjectPiece, 'id'>) => void;
  onPieceUpdate: (id: string, piece: Omit<ProjectPiece, 'id'>) => void;
  onPieceDelete: (id: string) => void;
  onPieceDuplicate: (id: string) => void;
  onOptimize: () => void;
  onSheetChange: (index: number) => void;
  onPrint: () => void;
  onExport: () => void;
  onProjectUpdate: (project: Partial<Project>) => void;
}

export class ProjectDetailView {
  private container: HTMLElement;
  private props: ProjectDetailViewProps;
  private tabContainer: TabContainer | null = null;
  private pieceForm: PieceForm | null = null;
  private pieceList: PieceList | null = null;
  private activeTabId: string = 'cutting-list';

  // Tab content containers
  private cuttingListContainer: HTMLElement | null = null;
  private visualizationContainer: HTMLElement | null = null;

  constructor(container: HTMLElement, props: ProjectDetailViewProps) {
    this.container = container;
    this.props = props;
    this.createView();
    this.initializeComponents();
  }

  /**
   * Update component props and re-render
   */
  updateProps(newProps: ProjectDetailViewProps): void {
    this.props = newProps;
    this.updateComponents();
  }

  /**
   * Create the project detail view structure
   */
  private createView(): void {
    this.container.innerHTML = `
      <div class="project-detail-view">
        <header class="project-header">
          <div class="project-info">
            <h1 class="project-title">${this.props.project.name}</h1>
            <div class="project-meta">
              <span class="piece-count">${this.props.project.pieces.length} pieces</span>
              <span class="saw-kerf">Saw kerf: ${this.props.project.sawKerf}${this.props.project.kerfUnit}</span>
            </div>
          </div>
          <div class="project-actions">
            <button class="optimize-btn action-btn" aria-label="Optimize cutting plan">
              OPTIMIZE CUTTING PLAN
            </button>
            <button class="export-btn" aria-label="Export cutting plan">
              Export Plan
            </button>
            <button class="print-btn" aria-label="Print cutting plan">
              Print Plan
            </button>
          </div>
        </header>

        <div class="project-content">
          <div class="tab-container-wrapper">
            <!-- Tab container will be rendered here -->
          </div>
        </div>
      </div>
    `;

    this.bindHeaderEvents();
  }

  /**
   * Bind header event listeners
   */
  private bindHeaderEvents(): void {
    const optimizeBtn = this.container.querySelector('.optimize-btn');
    const exportBtn = this.container.querySelector('.export-btn');
    const printBtn = this.container.querySelector('.print-btn');

    optimizeBtn?.addEventListener('click', () => this.props.onOptimize());
    exportBtn?.addEventListener('click', () => this.props.onExport());
    printBtn?.addEventListener('click', () => this.props.onPrint());
  }

  /**
   * Initialize tab container and components
   */
  private initializeComponents(): void {
    this.createTabContent();
    this.initializeTabContainer();
    this.initializePieceComponents();
  }

  /**
   * Create tab content containers
   */
  private createTabContent(): void {
    // Create cutting list tab content
    this.cuttingListContainer = document.createElement('div');
    this.cuttingListContainer.className = 'cutting-list-tab';
    this.cuttingListContainer.innerHTML = `
      <div class="pieces-section">
        <div class="section-header">
          <h2><span aria-hidden="true">📐</span> Project Pieces</h2>
        </div>
        <div class="piece-form-container">
          <!-- Piece form will be rendered here -->
        </div>
        <div class="piece-list-container">
          <!-- Piece list will be rendered here -->
        </div>
      </div>
      <div class="cutting-list-section">
        <div class="section-header">
          <h2><span aria-hidden="true">📝</span> Cutting List</h2>
        </div>
        <textarea 
          class="cutting-list-output" 
          readonly 
          aria-label="Detailed list of cuts"
          placeholder="Click 'OPTIMIZE CUTTING PLAN' to generate cutting list..."
        ></textarea>
      </div>
    `;

    // Create visualization tab content
    this.visualizationContainer = document.createElement('div');
    this.visualizationContainer.className = 'visualization-tab';
    this.visualizationContainer.innerHTML = `
      <div class="visualization-section">
        <div class="section-header">
          <h2><span aria-hidden="true">📊</span> Cutting Layout Visualization</h2>
        </div>
        <div class="canvas-container">
          <div 
            class="visualization-container" 
            role="img" 
            aria-label="Visualization of cutting layout on a base material sheet."
          ></div>
          <div class="sheet-navigation">
            <button class="prev-sheet-btn" type="button" aria-label="Previous sheet">
              &lt; Prev
            </button>
            <span class="sheet-indicator">Sheet 0 of 0</span>
            <button class="next-sheet-btn" type="button" aria-label="Next sheet">
              Next &gt;
            </button>
          </div>
        </div>
        <p class="optimization-message" aria-live="assertive"></p>
      </div>
    `;
  }

  /**
   * Initialize tab container
   */
  private initializeTabContainer(): void {
    const tabContainerWrapper = this.container.querySelector('.tab-container-wrapper');
    if (!tabContainerWrapper) return;

    const tabs: TabDefinition[] = [
      {
        id: 'cutting-list',
        label: 'Cutting List',
        content: this.cuttingListContainer!,
        icon: '📝'
      },
      {
        id: 'visualization',
        label: 'Visualization',
        content: this.visualizationContainer!,
        icon: '📊'
      }
    ];

    this.tabContainer = new TabContainer(tabContainerWrapper as HTMLElement, {
      tabs,
      activeTabId: this.activeTabId,
      onTabChange: (tabId) => this.handleTabChange(tabId)
    });
  }

  /**
   * Initialize piece form and list components
   */
  private initializePieceComponents(): void {
    if (!this.cuttingListContainer) return;

    const pieceFormContainer = this.cuttingListContainer.querySelector('.piece-form-container');
    const pieceListContainer = this.cuttingListContainer.querySelector('.piece-list-container');

    if (pieceFormContainer) {
      this.pieceForm = new PieceForm(pieceFormContainer as HTMLElement, {
        onAdd: (piece) => this.props.onPieceAdd(piece),
        onUpdate: (id, piece) => this.props.onPieceUpdate(id, piece),
        onCancelEdit: () => this.handleCancelPieceEdit(),
      });
    }

    if (pieceListContainer) {
      this.pieceList = new PieceList(pieceListContainer as HTMLElement, {
        pieces: this.props.project.pieces,
        onEdit: (piece) => this.handleEditPiece(piece),
        onDelete: (id) => this.props.onPieceDelete(id),
        onDuplicate: (id) => this.props.onPieceDuplicate(id),
      });
    }
  }

  /**
   * Handle tab change
   */
  private handleTabChange(tabId: string): void {
    this.activeTabId = tabId;
    
    // Update tab container
    if (this.tabContainer) {
      this.tabContainer.updateProps({
        tabs: this.tabContainer.getTabContainerElement() ? 
          (this.tabContainer as any).props.tabs : [],
        activeTabId: tabId,
        onTabChange: (id) => this.handleTabChange(id)
      });
    }

    // Handle tab-specific logic
    if (tabId === 'visualization') {
      this.updateVisualization();
    }
  }

  /**
   * Handle piece editing
   */
  private handleEditPiece(piece: ProjectPiece): void {
    if (this.pieceForm) {
      this.pieceForm.updateProps({
        onAdd: (p) => this.props.onPieceAdd(p),
        onUpdate: (id, p) => this.props.onPieceUpdate(id, p),
        editingPiece: piece,
        onCancelEdit: () => this.handleCancelPieceEdit(),
      });
    }
  }

  /**
   * Handle cancel piece edit
   */
  private handleCancelPieceEdit(): void {
    if (this.pieceForm) {
      this.pieceForm.updateProps({
        onAdd: (p) => this.props.onPieceAdd(p),
        onUpdate: (id, p) => this.props.onPieceUpdate(id, p),
        editingPiece: undefined,
        onCancelEdit: () => this.handleCancelPieceEdit(),
      });
    }
  }

  /**
   * Update all components with new props
   */
  private updateComponents(): void {
    // Update project header
    this.updateProjectHeader();
    
    // Update piece list
    if (this.pieceList) {
      this.pieceList.updateProps({
        pieces: this.props.project.pieces,
        onEdit: (piece) => this.handleEditPiece(piece),
        onDelete: (id) => this.props.onPieceDelete(id),
        onDuplicate: (id) => this.props.onPieceDuplicate(id),
      });
    }

    // Update cutting list output
    this.updateCuttingList();

    // Update visualization if active
    if (this.activeTabId === 'visualization') {
      this.updateVisualization();
    }
  }

  /**
   * Update project header information
   */
  private updateProjectHeader(): void {
    const titleElement = this.container.querySelector('.project-title');
    const pieceCountElement = this.container.querySelector('.piece-count');
    const sawKerfElement = this.container.querySelector('.saw-kerf');

    if (titleElement) titleElement.textContent = this.props.project.name;
    if (pieceCountElement) pieceCountElement.textContent = `${this.props.project.pieces.length} pieces`;
    if (sawKerfElement) sawKerfElement.textContent = `Saw kerf: ${this.props.project.sawKerf}${this.props.project.kerfUnit}`;
  }

  /**
   * Update cutting list output
   */
  private updateCuttingList(): void {
    const cuttingListOutput = this.container.querySelector('.cutting-list-output') as HTMLTextAreaElement;
    if (!cuttingListOutput) return;

    if (this.props.optimizedLayouts.length === 0) {
      cuttingListOutput.value = "Click 'OPTIMIZE CUTTING PLAN' to generate cutting list...";
      return;
    }

    // Generate cutting list text (simplified version)
    let cuttingList = `CUTTING LIST - ${this.props.project.name}\n`;
    cuttingList += `Generated: ${new Date().toLocaleDateString()}\n\n`;

    this.props.optimizedLayouts.forEach((layout, index) => {
      cuttingList += `SHEET ${index + 1}: ${layout.baseMaterial.name}\n`;
      cuttingList += `Material: ${layout.widthUsed} × ${layout.heightUsed} ${layout.baseMaterial.unit}\n\n`;
      
      layout.pieces.forEach((piece, pieceIndex) => {
        cuttingList += `  ${pieceIndex + 1}. ${piece.name} - ${piece.length} × ${piece.width} ${piece.unit}\n`;
      });
      cuttingList += '\n';
    });

    cuttingListOutput.value = cuttingList;
  }

  /**
   * Update visualization
   */
  private updateVisualization(): void {
    // This will be handled by the main app when it initializes the D3 renderer
    // in the visualization container
  }

  /**
   * Get visualization container for external renderer
   */
  getVisualizationContainer(): HTMLElement | null {
    return this.visualizationContainer?.querySelector('.visualization-container') as HTMLElement;
  }

  /**
   * Get sheet navigation elements
   */
  getSheetNavigationElements(): {
    prevButton: HTMLButtonElement | null;
    nextButton: HTMLButtonElement | null;
    indicator: HTMLSpanElement | null;
  } {
    return {
      prevButton: this.visualizationContainer?.querySelector('.prev-sheet-btn') as HTMLButtonElement,
      nextButton: this.visualizationContainer?.querySelector('.next-sheet-btn') as HTMLButtonElement,
      indicator: this.visualizationContainer?.querySelector('.sheet-indicator') as HTMLSpanElement,
    };
  }

  /**
   * Destroy the component
   */
  destroy(): void {
    if (this.tabContainer) {
      this.tabContainer.destroy();
      this.tabContainer = null;
    }

    if (this.pieceForm) {
      this.pieceForm.destroy();
      this.pieceForm = null;
    }

    if (this.pieceList) {
      this.pieceList.destroy();
      this.pieceList = null;
    }

    this.container.innerHTML = '';
  }
}
