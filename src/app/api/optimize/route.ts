import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'
import { OptimizationEngine } from '@/lib/optimization/server-engine'
import { z } from 'zod'

// Validation schema for optimization request
const OptimizationRequestSchema = z.object({
  materials: z.array(z.object({
    id: z.string(),
    name: z.string(),
    length: z.number().positive(),
    width: z.number().positive(),
    thickness: z.number().optional(),
    unit: z.enum(['mm', 'cm', 'm', 'in', 'ft']),
    quantity: z.number().int().positive(),
    cost: z.number().optional(),
  })),
  pieces: z.array(z.object({
    id: z.string(),
    name: z.string(),
    length: z.number().positive(),
    width: z.number().positive(),
    thickness: z.number().optional(),
    unit: z.enum(['mm', 'cm', 'm', 'in', 'ft']),
    quantity: z.number().int().positive(),
    grainDirection: z.enum(['NONE', 'HORIZONTAL', 'VERTICAL', 'EITHER']).optional(),
    priority: z.number().int().min(1).max(10).optional(),
  })),
  sawKerf: z.number().min(0),
  kerfUnit: z.enum(['mm', 'cm', 'm', 'in', 'ft']),
  projectId: z.string(),
})

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const supabase = createServerClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = OptimizationRequestSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      )
    }

    const optimizationRequest = validationResult.data

    // Rate limiting check (simple implementation)
    const userAgent = request.headers.get('user-agent') || ''
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown'
    
    // TODO: Implement proper rate limiting with Redis or similar
    console.log(`Optimization request from ${clientIP}, User-Agent: ${userAgent}`)

    // Initialize optimization engine
    const engine = new OptimizationEngine()

    // Perform optimization
    const startTime = Date.now()
    const result = await engine.optimize({
      ...optimizationRequest,
      userId: session.user.id,
    })

    const processingTime = Date.now() - startTime

    // Store optimization result in database
    if (result.success && result.layouts.length > 0) {
      try {
        await storeOptimizationResult(
          supabase,
          session.user.id,
          optimizationRequest.projectId,
          result,
          processingTime
        )
      } catch (error) {
        console.error('Failed to store optimization result:', error)
        // Continue anyway - the optimization was successful
      }
    }

    // Return result
    return NextResponse.json({
      success: result.success,
      layouts: result.layouts,
      metadata: {
        ...result.metadata,
        processingTime,
        algorithm: 'server-optimized',
        timestamp: new Date().toISOString(),
      },
      error: result.error,
    })

  } catch (error) {
    console.error('Optimization API error:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error during optimization',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

/**
 * Store optimization result in database
 */
async function storeOptimizationResult(
  supabase: any,
  userId: string,
  projectId: string,
  result: any,
  processingTime: number
) {
  // Store the optimization result
  const { data: optimizationResult, error: optimizationError } = await supabase
    .from('optimization_results')
    .insert({
      userId,
      projectId,
      totalSheets: result.layouts.length,
      totalWaste: result.metadata?.totalWaste || 0,
      efficiency: result.metadata?.efficiency || 0,
      processingTime,
      algorithm: 'server-optimized',
    })
    .select()
    .single()

  if (optimizationError) {
    throw new Error(`Failed to store optimization result: ${optimizationError.message}`)
  }

  // Store sheets and placed pieces
  for (let i = 0; i < result.layouts.length; i++) {
    const layout = result.layouts[i]
    
    const { data: sheet, error: sheetError } = await supabase
      .from('sheets')
      .insert({
        optimizationResultId: optimizationResult.id,
        sheetIndex: i,
        widthUsed: layout.widthUsed,
        heightUsed: layout.heightUsed,
        materialSnapshot: layout.baseMaterial,
      })
      .select()
      .single()

    if (sheetError) {
      throw new Error(`Failed to store sheet: ${sheetError.message}`)
    }

    // Store placed pieces for this sheet
    if (layout.pieces && layout.pieces.length > 0) {
      const placedPieces = layout.pieces.map((piece: any) => ({
        sheetId: sheet.id,
        pieceId: piece.id,
        x: piece.x,
        y: piece.y,
        packedWidth: piece.packedWidth,
        packedHeight: piece.packedHeight,
        rotation: piece.rotation || 0,
        color: piece.color,
        pieceSnapshot: piece,
      }))

      const { error: piecesError } = await supabase
        .from('placed_pieces')
        .insert(placedPieces)

      if (piecesError) {
        throw new Error(`Failed to store placed pieces: ${piecesError.message}`)
      }
    }
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
