import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus, FolderOpen, Package, Zap, TrendingUp } from 'lucide-react'
import Link from 'next/link'

export default async function DashboardPage() {
  const supabase = createServerComponentClient({ cookies })
  
  // Get user's projects and materials count
  const { data: projects } = await supabase
    .from('projects')
    .select('id, name, created_at')
    .order('updated_at', { ascending: false })
    .limit(5)

  const { data: materials } = await supabase
    .from('materials')
    .select('id')

  const { data: recentOptimizations } = await supabase
    .from('optimization_results')
    .select('id, total_sheets, efficiency, created_at, projects(name)')
    .order('created_at', { ascending: false })
    .limit(3)

  const projectCount = projects?.length || 0
  const materialCount = materials?.length || 0
  const avgEfficiency = recentOptimizations?.reduce((sum, opt) => sum + opt.efficiency, 0) / (recentOptimizations?.length || 1) || 0

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">
          Welcome back! Here's an overview of your woodworking projects.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
            <FolderOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{projectCount}</div>
            <p className="text-xs text-muted-foreground">
              Active woodworking projects
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Materials</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{materialCount}</div>
            <p className="text-xs text-muted-foreground">
              Available in inventory
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Optimizations</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{recentOptimizations?.length || 0}</div>
            <p className="text-xs text-muted-foreground">
              Recent optimizations
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Efficiency</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgEfficiency.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Material utilization
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Get started with your woodworking projects
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button asChild className="h-auto p-6 flex flex-col items-center space-y-2">
              <Link href="/dashboard/projects/new">
                <Plus className="h-8 w-8" />
                <span className="font-medium">New Project</span>
                <span className="text-sm text-center opacity-80">
                  Start a new woodworking project
                </span>
              </Link>
            </Button>

            <Button variant="outline" asChild className="h-auto p-6 flex flex-col items-center space-y-2">
              <Link href="/dashboard/inventory">
                <Package className="h-8 w-8" />
                <span className="font-medium">Manage Inventory</span>
                <span className="text-sm text-center opacity-80">
                  Add and organize materials
                </span>
              </Link>
            </Button>

            <Button variant="outline" asChild className="h-auto p-6 flex flex-col items-center space-y-2">
              <Link href="/dashboard/projects">
                <FolderOpen className="h-8 w-8" />
                <span className="font-medium">View Projects</span>
                <span className="text-sm text-center opacity-80">
                  Browse existing projects
                </span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Projects */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Projects</CardTitle>
            <CardDescription>
              Your latest woodworking projects
            </CardDescription>
          </CardHeader>
          <CardContent>
            {projects && projects.length > 0 ? (
              <div className="space-y-3">
                {projects.map((project) => (
                  <div key={project.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{project.name}</h4>
                      <p className="text-sm text-gray-600">
                        Created {new Date(project.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/dashboard/projects/${project.id}`}>
                        Open
                      </Link>
                    </Button>
                  </div>
                ))}
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/dashboard/projects">View All Projects</Link>
                </Button>
              </div>
            ) : (
              <div className="text-center py-6">
                <FolderOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-4">No projects yet</p>
                <Button asChild>
                  <Link href="/dashboard/projects/new">Create Your First Project</Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Optimizations</CardTitle>
            <CardDescription>
              Latest cutting plan optimizations
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentOptimizations && recentOptimizations.length > 0 ? (
              <div className="space-y-3">
                {recentOptimizations.map((optimization) => (
                  <div key={optimization.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{optimization.projects?.name || 'Unknown Project'}</h4>
                      <p className="text-sm text-gray-600">
                        {optimization.total_sheets} sheets, {optimization.efficiency.toFixed(1)}% efficiency
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">
                        {new Date(optimization.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-4">No optimizations yet</p>
                <p className="text-sm text-gray-500">
                  Create a project and add pieces to start optimizing
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
