import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { notFound } from 'next/navigation'
import { ProjectDetailClient } from '@/components/projects/project-detail-client'

interface ProjectDetailPageProps {
  params: {
    id: string
  }
}

export default async function ProjectDetailPage({ params }: ProjectDetailPageProps) {
  const supabase = createServerComponentClient({ cookies })
  
  // Get project with pieces
  const { data: project, error } = await supabase
    .from('projects')
    .select(`
      *,
      pieces(*)
    `)
    .eq('id', params.id)
    .single()

  if (error || !project) {
    notFound()
  }

  // Get user's materials for optimization
  const { data: materials } = await supabase
    .from('materials')
    .select('*')
    .order('name')

  // Get recent optimization results for this project
  const { data: optimizationResults } = await supabase
    .from('optimization_results')
    .select(`
      *,
      sheets(
        *,
        placed_pieces(*)
      )
    `)
    .eq('project_id', params.id)
    .order('created_at', { ascending: false })
    .limit(1)

  return (
    <ProjectDetailClient
      project={project}
      materials={materials || []}
      initialOptimizationResult={optimizationResults?.[0] || null}
    />
  )
}
