'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ArrowLeft, Save } from 'lucide-react'
import Link from 'next/link'
import { useToast } from '@/hooks/use-toast'

export default function NewProjectPage() {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    sawKerf: '3',
    kerfUnit: 'MM' as const,
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  
  const router = useRouter()
  const supabase = createClient()
  const { toast } = useToast()

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!formData.name.trim()) {
      setError('Project name is required')
      setLoading(false)
      return
    }

    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) {
        setError('You must be logged in to create a project')
        setLoading(false)
        return
      }

      const { data, error } = await supabase
        .from('projects')
        .insert({
          name: formData.name.trim(),
          description: formData.description.trim() || null,
          saw_kerf: parseFloat(formData.sawKerf),
          kerf_unit: formData.kerfUnit,
          user_id: session.user.id,
        })
        .select()
        .single()

      if (error) {
        setError(error.message)
        return
      }

      toast({
        title: 'Success',
        description: 'Project created successfully!',
      })

      // Redirect to the new project
      router.push(`/dashboard/projects/${data.id}`)
    } catch (err) {
      setError('An unexpected error occurred')
      console.error('Error creating project:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/projects">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Projects
          </Link>
        </Button>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>Create New Project</CardTitle>
          <CardDescription>
            Set up a new woodworking project with materials and pieces
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Project Name */}
            <div className="space-y-2">
              <Label htmlFor="name">Project Name *</Label>
              <Input
                id="name"
                type="text"
                placeholder="Enter project name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
                disabled={loading}
              />
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Optional project description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                disabled={loading}
                rows={3}
              />
            </div>

            {/* Saw Kerf Settings */}
            <div className="space-y-4">
              <Label>Saw Kerf Settings</Label>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sawKerf">Kerf Width</Label>
                  <Input
                    id="sawKerf"
                    type="number"
                    step="0.1"
                    min="0"
                    placeholder="3"
                    value={formData.sawKerf}
                    onChange={(e) => handleInputChange('sawKerf', e.target.value)}
                    disabled={loading}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="kerfUnit">Unit</Label>
                  <Select
                    value={formData.kerfUnit}
                    onValueChange={(value) => handleInputChange('kerfUnit', value)}
                    disabled={loading}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MM">Millimeters (mm)</SelectItem>
                      <SelectItem value="CM">Centimeters (cm)</SelectItem>
                      <SelectItem value="IN">Inches (in)</SelectItem>
                      <SelectItem value="FT">Feet (ft)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                Saw kerf is the width of material removed by the cutting blade. 
                This is typically 2-4mm for most table saw blades.
              </p>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" asChild disabled={loading}>
                <Link href="/dashboard/projects">Cancel</Link>
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  'Creating...'
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Create Project
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Help Text */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Next Steps</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-gray-600">
            <p>After creating your project, you'll be able to:</p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Add pieces with dimensions and grain direction preferences</li>
              <li>Manage your material inventory</li>
              <li>Run optimization algorithms to generate cutting plans</li>
              <li>View interactive visualizations of your layouts</li>
              <li>Export and print cutting lists for your workshop</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
