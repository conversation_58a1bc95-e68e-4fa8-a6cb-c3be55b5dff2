import { createServerClient } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus, Package, Edit, Trash2, DollarSign } from 'lucide-react'
import Link from 'next/link'
import { formatNumber } from '@/lib/utils'

export default async function InventoryPage() {
  const supabase = createServerClient()
  
  // Get user's materials
  const { data: materials, error } = await supabase
    .from('materials')
    .select('*')
    .order('updated_at', { ascending: false })

  if (error) {
    console.error('Error fetching materials:', error)
  }

  const totalMaterials = materials?.length || 0
  const totalValue = materials?.reduce((sum, material) => 
    sum + ((material.cost || 0) * material.quantity), 0) || 0

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Material Inventory</h1>
          <p className="text-gray-600 mt-2">
            Manage your woodworking materials and track inventory
          </p>
        </div>
        <Button asChild>
          <Link href="/dashboard/inventory/new">
            <Plus className="h-4 w-4 mr-2" />
            Add Material
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Materials</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalMaterials}</div>
            <p className="text-xs text-muted-foreground">
              Different material types
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Quantity</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {materials?.reduce((sum, material) => sum + material.quantity, 0) || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Individual pieces/sheets
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${formatNumber(totalValue)}</div>
            <p className="text-xs text-muted-foreground">
              Estimated inventory value
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Materials List */}
      {materials && materials.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {materials.map((material) => (
            <Card key={material.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{material.name}</CardTitle>
                    {material.supplier && (
                      <CardDescription className="mt-1">
                        Supplier: {material.supplier}
                      </CardDescription>
                    )}
                  </div>
                  <div className="flex space-x-1">
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/dashboard/inventory/${material.id}/edit`}>
                        <Edit className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Dimensions */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Dimensions:</span>
                      <span className="font-medium">
                        {formatNumber(material.length)} × {formatNumber(material.width)}
                        {material.thickness && ` × ${formatNumber(material.thickness)}`} {material.unit}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Quantity:</span>
                      <span className="font-medium">{material.quantity}</span>
                    </div>

                    {material.cost && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Cost per unit:</span>
                        <span className="font-medium">${formatNumber(material.cost)}</span>
                      </div>
                    )}

                    {material.cost && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Total value:</span>
                        <span className="font-medium">${formatNumber(material.cost * material.quantity)}</span>
                      </div>
                    )}
                  </div>

                  {/* Notes */}
                  {material.notes && (
                    <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                      {material.notes}
                    </div>
                  )}

                  {/* Area calculation */}
                  <div className="text-xs text-gray-500 pt-2 border-t">
                    Area: {formatNumber((material.length * material.width) / 1000000)} m² per piece
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        /* Empty State */
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-16">
            <Package className="h-16 w-16 text-gray-400 mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No materials yet</h3>
            <p className="text-gray-600 text-center mb-6 max-w-md">
              Start building your material inventory by adding wood sheets, lumber, 
              and other materials you have available for your projects.
            </p>
            <Button asChild>
              <Link href="/dashboard/inventory/new">
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Material
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Material Management Tips</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Adding Materials</h4>
              <ul className="space-y-1">
                <li>• Include accurate dimensions for optimal cutting plans</li>
                <li>• Track costs to monitor project budgets</li>
                <li>• Add supplier information for reordering</li>
                <li>• Use notes for material grades or special properties</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Best Practices</h4>
              <ul className="space-y-1">
                <li>• Update quantities as you use materials</li>
                <li>• Group similar materials for easier management</li>
                <li>• Include thickness for 3D optimization</li>
                <li>• Regular inventory audits ensure accuracy</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
