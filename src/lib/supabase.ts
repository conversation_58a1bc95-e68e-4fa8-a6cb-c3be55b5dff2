import { createBrowserClient } from '@supabase/ssr'

export const createClient = () => {
  // For development without Supabase setup
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL ||
      process.env.NEXT_PUBLIC_SUPABASE_URL === 'https://your-project.supabase.co') {
    return null as any // Return null for development
  }

  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          username: string | null
          name: string | null
          avatar: string | null
          plan: 'FREE' | 'PRO' | 'ENTERPRISE'
          password: string
          email_verified: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          username?: string | null
          name?: string | null
          avatar?: string | null
          plan?: 'FREE' | 'PRO' | 'ENTERPRISE'
          password: string
          email_verified?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          username?: string | null
          name?: string | null
          avatar?: string | null
          plan?: 'FREE' | 'PRO' | 'ENTERPRISE'
          password?: string
          email_verified?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          name: string
          description: string | null
          saw_kerf: number
          kerf_unit: 'MM' | 'CM' | 'M' | 'IN' | 'FT'
          created_at: string
          updated_at: string
          user_id: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          saw_kerf?: number
          kerf_unit?: 'MM' | 'CM' | 'M' | 'IN' | 'FT'
          created_at?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          saw_kerf?: number
          kerf_unit?: 'MM' | 'CM' | 'M' | 'IN' | 'FT'
          created_at?: string
          updated_at?: string
          user_id?: string
        }
      }
      materials: {
        Row: {
          id: string
          name: string
          length: number
          width: number
          thickness: number | null
          unit: 'MM' | 'CM' | 'M' | 'IN' | 'FT'
          quantity: number
          cost: number | null
          supplier: string | null
          notes: string | null
          created_at: string
          updated_at: string
          user_id: string
        }
        Insert: {
          id?: string
          name: string
          length: number
          width: number
          thickness?: number | null
          unit?: 'MM' | 'CM' | 'M' | 'IN' | 'FT'
          quantity?: number
          cost?: number | null
          supplier?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          id?: string
          name?: string
          length?: number
          width?: number
          thickness?: number | null
          unit?: 'MM' | 'CM' | 'M' | 'IN' | 'FT'
          quantity?: number
          cost?: number | null
          supplier?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
          user_id?: string
        }
      }
      pieces: {
        Row: {
          id: string
          name: string
          length: number
          width: number
          thickness: number | null
          unit: 'MM' | 'CM' | 'M' | 'IN' | 'FT'
          quantity: number
          grain_direction: 'NONE' | 'HORIZONTAL' | 'VERTICAL' | 'EITHER'
          priority: number
          notes: string | null
          created_at: string
          updated_at: string
          project_id: string
        }
        Insert: {
          id?: string
          name: string
          length: number
          width: number
          thickness?: number | null
          unit?: 'MM' | 'CM' | 'M' | 'IN' | 'FT'
          quantity?: number
          grain_direction?: 'NONE' | 'HORIZONTAL' | 'VERTICAL' | 'EITHER'
          priority?: number
          notes?: string | null
          created_at?: string
          updated_at?: string
          project_id: string
        }
        Update: {
          id?: string
          name?: string
          length?: number
          width?: number
          thickness?: number | null
          unit?: 'MM' | 'CM' | 'M' | 'IN' | 'FT'
          quantity?: number
          grain_direction?: 'NONE' | 'HORIZONTAL' | 'VERTICAL' | 'EITHER'
          priority?: number
          notes?: string | null
          created_at?: string
          updated_at?: string
          project_id?: string
        }
      }
      optimization_results: {
        Row: {
          id: string
          total_sheets: number
          total_waste: number
          efficiency: number
          processing_time: number
          algorithm: string
          created_at: string
          user_id: string
          project_id: string
        }
        Insert: {
          id?: string
          total_sheets: number
          total_waste: number
          efficiency: number
          processing_time: number
          algorithm?: string
          created_at?: string
          user_id: string
          project_id: string
        }
        Update: {
          id?: string
          total_sheets?: number
          total_waste?: number
          efficiency?: number
          processing_time?: number
          algorithm?: string
          created_at?: string
          user_id?: string
          project_id?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_plan: 'FREE' | 'PRO' | 'ENTERPRISE'
      unit: 'MM' | 'CM' | 'M' | 'IN' | 'FT'
      grain_direction: 'NONE' | 'HORIZONTAL' | 'VERTICAL' | 'EITHER'
    }
  }
}
