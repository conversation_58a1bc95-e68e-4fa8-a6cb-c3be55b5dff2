# Woodworking Cut Optimizer - Setup Guide

This guide will help you set up the full-stack Next.js woodworking application with authentication and database integration.

## Prerequisites

- Node.js 18+ installed
- PostgreSQL database (local or cloud)
- Supabase account (for production)
- Git

## 1. Environment Setup

### Local Development Database

1. **Install PostgreSQL locally:**
   ```bash
   # macOS with Homebrew
   brew install postgresql
   brew services start postgresql
   
   # Ubuntu/Debian
   sudo apt update
   sudo apt install postgresql postgresql-contrib
   sudo systemctl start postgresql
   
   # Windows
   # Download and install from https://www.postgresql.org/download/windows/
   ```

2. **Create database:**
   ```bash
   # Connect to PostgreSQL
   psql postgres
   
   # Create database and user
   CREATE DATABASE woodworking_optimizer;
   CREATE USER woodworking_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE woodworking_optimizer TO woodworking_user;
   \q
   ```

### Supabase Setup (Production)

1. **Create Supabase project:**
   - Go to [supabase.com](https://supabase.com)
   - Create new project
   - Note down your project URL and anon key

2. **Configure authentication:**
   - Enable email authentication
   - Configure email templates (optional)
   - Set up custom SMTP (optional)

## 2. Project Setup

### Clone and Install Dependencies

```bash
# Clone the repository
git clone <your-repo-url>
cd woodworking-optimizer

# Install dependencies
npm install

# Install additional dependencies if needed
npm install tailwindcss-animate @radix-ui/react-avatar @radix-ui/react-dropdown-menu
```

### Environment Configuration

1. **Copy environment file:**
   ```bash
   cp .env.example .env.local
   ```

2. **Configure environment variables:**
   ```env
   # Database Configuration
   DATABASE_URL="postgresql://woodworking_user:your_password@localhost:5432/woodworking_optimizer"
   
   # Supabase Configuration (Production)
   NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
   NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
   SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"
   
   # NextAuth Configuration
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-nextauth-secret-key"
   
   # Application Configuration
   NODE_ENV="development"
   NEXT_PUBLIC_APP_URL="http://localhost:3000"
   ```

## 3. Database Setup

### Initialize Prisma

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database (for development)
npm run db:push

# Or run migrations (for production)
npm run db:migrate

# Open Prisma Studio to view data
npm run db:studio
```

### Seed Database (Optional)

Create a seed file to populate initial data:

```bash
# Create seed file
touch prisma/seed.ts
```

Add seed script to `package.json`:
```json
{
  "prisma": {
    "seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"
  }
}
```

## 4. Development

### Start Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

### Development Workflow

1. **Database changes:**
   - Modify `prisma/schema.prisma`
   - Run `npm run db:push` for development
   - Run `npm run db:migrate` for production

2. **Type checking:**
   ```bash
   npm run type-check
   ```

3. **Linting:**
   ```bash
   npm run lint
   ```

## 5. Production Deployment

### Supabase Production Setup

1. **Configure Supabase:**
   - Set up production database
   - Configure authentication providers
   - Set up row-level security (RLS) policies

2. **Database migration:**
   ```bash
   # Set production DATABASE_URL
   export DATABASE_URL="your-production-database-url"
   
   # Run migrations
   npm run db:migrate
   ```

### Deployment Options

#### Vercel (Recommended)

1. **Connect repository to Vercel**
2. **Configure environment variables**
3. **Deploy**

#### Other Platforms

- **Netlify:** Configure build settings
- **Railway:** Connect database and deploy
- **DigitalOcean App Platform:** Configure app spec

## 6. Features Overview

### Authentication
- User registration and login
- Email verification
- Password reset
- Protected routes
- User profile management

### Project Management
- Create and manage woodworking projects
- Add pieces with dimensions and properties
- Project-specific settings (saw kerf, units)

### Material Inventory
- Add and manage materials
- Track quantities and costs
- Material specifications

### Cut Optimization
- Server-side optimization algorithms
- Multiple optimization strategies
- Interactive visualization with d3.js
- Efficiency calculations

### Data Visualization
- Interactive cutting layouts
- Clickable pieces with details
- Print-friendly layouts
- Export functionality

## 7. API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout

### Projects
- `GET /api/projects` - List user projects
- `POST /api/projects` - Create project
- `GET /api/projects/[id]` - Get project details
- `PUT /api/projects/[id]` - Update project
- `DELETE /api/projects/[id]` - Delete project

### Materials
- `GET /api/materials` - List user materials
- `POST /api/materials` - Create material
- `PUT /api/materials/[id]` - Update material
- `DELETE /api/materials/[id]` - Delete material

### Optimization
- `POST /api/optimize` - Run optimization

## 8. Security Considerations

### Database Security
- Row-level security (RLS) enabled
- User data isolation
- Input validation and sanitization

### API Security
- Authentication required for all endpoints
- Rate limiting implemented
- CORS configuration

### Optimization Security
- Server-side algorithms only
- No client-side exposure of IP
- Request validation and limits

## 9. Troubleshooting

### Common Issues

1. **Database connection errors:**
   - Check DATABASE_URL format
   - Verify database is running
   - Check user permissions

2. **Authentication issues:**
   - Verify Supabase configuration
   - Check environment variables
   - Ensure NEXTAUTH_SECRET is set

3. **Build errors:**
   - Run `npm run type-check`
   - Check for missing dependencies
   - Verify environment variables

### Getting Help

- Check the application logs
- Review environment configuration
- Verify database schema
- Test API endpoints individually

## 10. Migration from Legacy Version

If migrating from the previous client-only version:

1. **Export existing data:**
   - Projects from localStorage
   - Materials from localStorage
   - User preferences

2. **Import to new system:**
   - Create user account
   - Import projects via API
   - Import materials via API

3. **Verify functionality:**
   - Test optimization
   - Check visualizations
   - Verify print functionality

## Next Steps

After setup is complete:

1. **Customize the application:**
   - Modify optimization algorithms
   - Add new features
   - Customize UI components

2. **Scale the application:**
   - Add caching layer
   - Implement background jobs
   - Add monitoring and analytics

3. **Extend functionality:**
   - Add collaboration features
   - Implement file uploads
   - Add reporting and analytics
