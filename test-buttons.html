<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .log {
            background-color: #f4f4f4;
            padding: 10px;
            margin: 20px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Button Functionality Test</h1>
    
    <div>
        <button id="createProjectBtn">Create Project</button>
        <button id="addMaterialBtn">Add Material</button>
        <button id="addPieceBtn">Add Piece</button>
    </div>
    
    <div class="log" id="log">Click the buttons above to test functionality...\n</div>
    
    <script>
        function log(message) {
            const logElement = document.getElementById('log');
            logElement.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }
        
        // Test button event handlers
        document.getElementById('createProjectBtn').addEventListener('click', function() {
            log('Create Project button clicked!');
        });
        
        document.getElementById('addMaterialBtn').addEventListener('click', function() {
            log('Add Material button clicked!');
        });
        
        document.getElementById('addPieceBtn').addEventListener('click', function() {
            log('Add Piece button clicked!');
        });
        
        log('Button test page loaded successfully');
    </script>
</body>
</html>
