<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Cutting List</title>
    <script src="https://cdn.jsdelivr.net/gh/jakesgordon/bin-packing@master/js/packer.js"></script>
</head>
<body>
    <h1>Cutting List Test</h1>
    <div id="status">Loading...</div>
    <div id="results"></div>
    
    <script type="module">
        import { WoodworkingApp } from './src/main.js';
        
        async function testCuttingList() {
            const statusEl = document.getElementById('status');
            const resultsEl = document.getElementById('results');
            
            try {
                statusEl.textContent = 'Creating app...';
                
                // Create app instance
                const app = new WoodworkingApp({
                    enableServerOptimization: false,
                    enableAuthentication: false,
                });
                
                statusEl.textContent = 'Initializing app...';
                await app.initialize();
                
                statusEl.textContent = 'Adding test data...';
                
                // Add test material
                await app.materialManager.addMaterial({
                    name: 'Test Plywood',
                    length: 1200,
                    width: 800,
                    unit: 'mm',
                    quantity: 5,
                    thickness: '18mm'
                });
                
                // Create test project
                const projectId = await app.projectManager.createProject('Test Project');
                await app.projectManager.setCurrentProject(projectId);
                
                // Add test pieces
                const project = app.projectManager.getCurrentProject();
                if (project) {
                    await app.projectManager.addPiece(project.id, {
                        name: 'Shelf',
                        length: 600,
                        width: 300,
                        unit: 'mm',
                        quantity: 4,
                        grainDirection: 'length'
                    });
                    
                    await app.projectManager.addPiece(project.id, {
                        name: 'Side Panel',
                        length: 800,
                        width: 400,
                        unit: 'mm',
                        quantity: 2,
                        grainDirection: 'length'
                    });
                }
                
                statusEl.textContent = 'Running optimization...';
                
                // Run optimization
                const currentProject = app.projectManager.getCurrentProject();
                const materials = app.materialManager.getMaterials();
                
                if (currentProject && materials.length > 0) {
                    const request = {
                        materials,
                        pieces: currentProject.pieces,
                        sawKerf: currentProject.sawKerf,
                        kerfUnit: currentProject.kerfUnit,
                        projectId: currentProject.id,
                    };
                    
                    const response = await app.optimizationClient.optimize(request);
                    
                    if (response.success) {
                        statusEl.textContent = 'Optimization successful! Checking cutting list...';
                        
                        // Check if cutting list is populated
                        setTimeout(() => {
                            const cuttingListEl = document.querySelector('#cuttingListOutput');
                            if (cuttingListEl && cuttingListEl.value) {
                                statusEl.textContent = 'SUCCESS: Cutting list is populated!';
                                statusEl.style.color = 'green';
                                resultsEl.innerHTML = `<h3>Cutting List Content:</h3><pre>${cuttingListEl.value}</pre>`;
                            } else {
                                statusEl.textContent = 'FAILED: Cutting list is empty!';
                                statusEl.style.color = 'red';
                                resultsEl.innerHTML = '<p>Cutting list textarea not found or empty</p>';
                            }
                        }, 1000);
                    } else {
                        statusEl.textContent = `Optimization failed: ${response.error}`;
                        statusEl.style.color = 'red';
                    }
                } else {
                    statusEl.textContent = 'No project or materials found';
                    statusEl.style.color = 'red';
                }
                
            } catch (error) {
                statusEl.textContent = `Error: ${error.message}`;
                statusEl.style.color = 'red';
                console.error('Test error:', error);
            }
        }
        
        // Check if Packer is available
        if (typeof Packer === 'undefined') {
            document.getElementById('status').textContent = 'ERROR: Packer library not loaded';
            document.getElementById('status').style.color = 'red';
        } else {
            testCuttingList();
        }
    </script>
</body>
</html>
