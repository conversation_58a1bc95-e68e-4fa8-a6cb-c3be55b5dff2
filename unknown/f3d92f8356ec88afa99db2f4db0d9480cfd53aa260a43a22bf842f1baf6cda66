/**
 * Material Form Component - Handles material input and editing
 */

import { BaseMaterial, MaterialFormProps, Unit } from '../../types';
import { isPositiveNumber } from '../../utils';

export class MaterialForm {
  private container: HTMLElement;
  private props: MaterialFormProps;
  private isEditing: boolean = false;

  // Form elements
  private nameInput: HTMLInputElement;
  private lengthInput: HTMLInputElement;
  private widthInput: HTMLInputElement;
  private unitSelect: HTMLSelectElement;
  private quantityInput: HTMLInputElement;
  private thicknessInput: HTMLInputElement;
  private submitButton: HTMLButtonElement;
  private cancelButton: HTMLButtonElement;

  constructor(container: HTMLElement, props: MaterialFormProps) {
    this.container = container;
    this.props = props;
    this.createForm();
    this.bindEvents();
  }

  /**
   * Update props and re-render if needed
   */
  updateProps(newProps: MaterialFormProps): void {
    const wasEditing = this.isEditing;
    this.props = newProps;
    this.isEditing = !!newProps.editingMaterial;

    if (this.isEditing && newProps.editingMaterial) {
      this.populateForm(newProps.editingMaterial);
      this.submitButton.textContent = 'Update Material';
      this.cancelButton.style.display = 'inline-block';
    } else if (wasEditing && !this.isEditing) {
      this.resetForm();
    }
  }

  /**
   * Create the form HTML structure
   */
  private createForm(): void {
    this.container.innerHTML = `
      <div class="form-group">
        <label for="materialName">Material Name (e.g., Plywood Sheet)</label>
        <input type="text" id="materialName" placeholder="Plywood 3/4in" />
      </div>
      <div class="inline-inputs">
        <div class="form-group">
          <label for="materialLength">Length</label>
          <input type="number" id="materialLength" placeholder="e.g., 2440" />
        </div>
        <div class="form-group">
          <label for="materialWidth">Width</label>
          <input type="number" id="materialWidth" placeholder="e.g., 1220" />
        </div>
        <div class="form-group">
          <label for="materialUnit">Unit</label>
          <select id="materialUnit">
            <option value="mm">mm</option>
            <option value="cm">cm</option>
            <option value="in">inches</option>
          </select>
        </div>
      </div>
      <div class="inline-inputs">
        <div class="form-group">
          <label for="materialQuantity">Quantity</label>
          <input type="number" id="materialQuantity" value="1" min="1" />
        </div>
        <div class="form-group">
          <label for="materialThickness">Thickness (Optional)</label>
          <input type="text" id="materialThickness" placeholder="e.g., 18mm or 3/4in" />
        </div>
      </div>
      <div class="form-buttons">
        <button id="submitMaterialBtn" type="button" aria-label="Add material to inventory">
          Add Material
        </button>
        <button id="cancelMaterialBtn" type="button" class="secondary" style="display: none;" aria-label="Cancel editing">
          Cancel
        </button>
      </div>
    `;

    // Get references to form elements
    this.nameInput = this.container.querySelector('#materialName') as HTMLInputElement;
    this.lengthInput = this.container.querySelector('#materialLength') as HTMLInputElement;
    this.widthInput = this.container.querySelector('#materialWidth') as HTMLInputElement;
    this.unitSelect = this.container.querySelector('#materialUnit') as HTMLSelectElement;
    this.quantityInput = this.container.querySelector('#materialQuantity') as HTMLInputElement;
    this.thicknessInput = this.container.querySelector('#materialThickness') as HTMLInputElement;
    this.submitButton = this.container.querySelector('#submitMaterialBtn') as HTMLButtonElement;
    this.cancelButton = this.container.querySelector('#cancelMaterialBtn') as HTMLButtonElement;
  }

  /**
   * Bind event listeners
   */
  private bindEvents(): void {
    this.submitButton.addEventListener('click', () => this.handleSubmit());
    this.cancelButton.addEventListener('click', () => this.handleCancel());

    // Add input validation
    this.lengthInput.addEventListener('input', () => this.validateInput(this.lengthInput));
    this.widthInput.addEventListener('input', () => this.validateInput(this.widthInput));
    this.quantityInput.addEventListener('input', () => this.validateInput(this.quantityInput));

    // Handle Enter key
    this.container.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        this.handleSubmit();
      }
    });
  }

  /**
   * Handle form submission
   */
  private handleSubmit(): void {
    try {
      const materialData = this.getMaterialData();
      this.validateMaterialData(materialData);

      if (this.isEditing && this.props.editingMaterial) {
        this.props.onUpdate(this.props.editingMaterial.id, materialData);
      } else {
        this.props.onAdd(materialData);
      }

      this.resetForm();
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Invalid material data');
    }
  }

  /**
   * Handle cancel editing
   */
  private handleCancel(): void {
    this.props.onCancelEdit();
    this.resetForm();
  }

  /**
   * Get material data from form
   */
  private getMaterialData(): Omit<BaseMaterial, 'id'> {
    const name = this.nameInput.value.trim() || `Material ${Date.now()}`;
    const length = parseFloat(this.lengthInput.value);
    const width = parseFloat(this.widthInput.value);
    const unit = this.unitSelect.value as Unit;
    const quantity = parseInt(this.quantityInput.value, 10);
    const thickness = this.thicknessInput.value.trim();

    return {
      name,
      length,
      width,
      unit,
      quantity,
      thickness: thickness || undefined,
    };
  }

  /**
   * Validate material data
   */
  private validateMaterialData(data: Omit<BaseMaterial, 'id'>): void {
    if (!data.name) {
      throw new Error('Material name is required');
    }

    if (!isPositiveNumber(data.length)) {
      throw new Error('Length must be a positive number');
    }

    if (!isPositiveNumber(data.width)) {
      throw new Error('Width must be a positive number');
    }

    if (!isPositiveNumber(data.quantity)) {
      throw new Error('Quantity must be a positive number');
    }

    if (!['mm', 'cm', 'in'].includes(data.unit)) {
      throw new Error('Invalid unit selected');
    }
  }

  /**
   * Validate individual input field
   */
  private validateInput(input: HTMLInputElement): void {
    const value = parseFloat(input.value);
    
    if (input.type === 'number') {
      if (isNaN(value) || value <= 0) {
        input.setCustomValidity('Must be a positive number');
        input.classList.add('invalid');
      } else {
        input.setCustomValidity('');
        input.classList.remove('invalid');
      }
    }
  }

  /**
   * Populate form with material data for editing
   */
  private populateForm(material: BaseMaterial): void {
    this.nameInput.value = material.name;
    this.lengthInput.value = material.length.toString();
    this.widthInput.value = material.width.toString();
    this.unitSelect.value = material.unit;
    this.quantityInput.value = material.quantity.toString();
    this.thicknessInput.value = material.thickness || '';
  }

  /**
   * Reset form to initial state
   */
  private resetForm(): void {
    this.nameInput.value = '';
    this.lengthInput.value = '';
    this.widthInput.value = '';
    this.quantityInput.value = '1';
    this.thicknessInput.value = '';
    this.submitButton.textContent = 'Add Material';
    this.cancelButton.style.display = 'none';
    this.isEditing = false;

    // Clear validation states
    [this.lengthInput, this.widthInput, this.quantityInput].forEach(input => {
      input.setCustomValidity('');
      input.classList.remove('invalid');
    });
  }

  /**
   * Focus on the first input
   */
  focus(): void {
    this.nameInput.focus();
  }

  /**
   * Get current form state
   */
  getFormState(): {
    isEditing: boolean;
    hasData: boolean;
    isValid: boolean;
  } {
    const hasData = !!(
      this.nameInput.value.trim() ||
      this.lengthInput.value ||
      this.widthInput.value ||
      this.quantityInput.value !== '1' ||
      this.thicknessInput.value.trim()
    );

    let isValid = true;
    try {
      if (hasData) {
        this.validateMaterialData(this.getMaterialData());
      }
    } catch {
      isValid = false;
    }

    return {
      isEditing: this.isEditing,
      hasData,
      isValid,
    };
  }

  /**
   * Set form enabled/disabled state
   */
  setEnabled(enabled: boolean): void {
    const inputs = [
      this.nameInput,
      this.lengthInput,
      this.widthInput,
      this.unitSelect,
      this.quantityInput,
      this.thicknessInput,
    ];

    inputs.forEach(input => {
      input.disabled = !enabled;
    });

    this.submitButton.disabled = !enabled;
    this.cancelButton.disabled = !enabled;
  }

  /**
   * Destroy the component
   */
  destroy(): void {
    // Remove event listeners and clean up
    this.container.innerHTML = '';
  }
}
