/**
 * Storage interface abstraction for supporting both localStorage and future API storage
 */

import { BaseMaterial, Project, StorageInterface } from '../types';

export abstract class BaseStorage implements StorageInterface {
  abstract getInventory(): Promise<BaseMaterial[]>;
  abstract saveInventory(inventory: BaseMaterial[]): Promise<void>;
  abstract getProjects(): Promise<Project[]>;
  abstract saveProjects(projects: Project[]): Promise<void>;
  abstract getCurrentProjectId(): Promise<string | null>;
  abstract setCurrentProjectId(projectId: string | null): Promise<void>;
  abstract migrateOldData(): Promise<void>;
  abstract clearStorage(): Promise<void>;

  // Common utility methods that can be shared
  protected validateMaterial(material: BaseMaterial): void {
    if (!material.id || !material.name) {
      throw new Error('Material must have id and name');
    }
    if (material.length <= 0 || material.width <= 0) {
      throw new Error('Material dimensions must be positive');
    }
    if (material.quantity <= 0) {
      throw new Error('Material quantity must be positive');
    }
  }

  protected validateProject(project: Project): void {
    if (!project.id || !project.name) {
      throw new Error('Project must have id and name');
    }
    if (!Array.isArray(project.pieces)) {
      throw new Error('Project pieces must be an array');
    }
    if (project.sawKerf < 0) {
      throw new Error('Saw kerf cannot be negative');
    }
  }

  protected serializeProject(project: Project): any {
    return {
      ...project,
      createdAt: project.createdAt.toISOString(),
      updatedAt: project.updatedAt.toISOString(),
    };
  }

  protected deserializeProject(data: any): Project {
    return {
      ...data,
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt),
    };
  }
}
