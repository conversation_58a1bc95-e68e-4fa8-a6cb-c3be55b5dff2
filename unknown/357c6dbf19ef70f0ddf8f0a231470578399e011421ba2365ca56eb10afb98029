/**
 * Woodworking Cut Optimizer - Refactored Entry Point
 *
 * This application has been completely refactored from a monolithic structure
 * into a modern, modular architecture that supports:
 *
 * - Clean separation of concerns
 * - Proper TypeScript types and interfaces
 * - Modular component architecture
 * - Client-server architecture preparation
 * - Protected intellectual property (server-side optimization)
 * - Comprehensive error handling and logging
 * - Backward compatibility with existing localStorage data
 *
 * The new architecture includes:
 * - Storage abstraction layer (localStorage + future API support)
 * - Business logic managers (ProjectManager, MaterialManager)
 * - Optimization engine with server-side protection
 * - Reusable UI components
 * - Authentication and authorization framework
 * - Event-driven architecture with proper error handling
 */

// Import the new modular application
import { WoodworkingApp } from './src/main';

// Initialize the application with default configuration
const app = new WoodworkingApp({
  enableServerOptimization: false, // Start with local optimization
  enableAuthentication: false,     // Authentication disabled for now
  autoSaveInterval: 30000,         // Auto-save every 30 seconds
});

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
  try {
    await app.initialize();
    console.log('Woodworking Cut Optimizer initialized successfully');

    // Make app available globally for debugging and future extensions
    (window as any).woodworkingApp = app;
  } catch (error) {
    console.error('Failed to initialize application:', error);
    alert('Failed to initialize the application. Please refresh the page and try again.');
  }
});
