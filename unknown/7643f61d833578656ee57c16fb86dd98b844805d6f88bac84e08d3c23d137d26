<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Woodworking Cut Optimizer - Refactoring Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .inline-inputs {
            display: flex;
            gap: 15px;
        }
        
        .inline-inputs .form-group {
            flex: 1;
        }
        
        .form-buttons {
            margin-top: 20px;
        }
        
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        
        button:hover {
            background: #005a87;
        }
        
        button.secondary {
            background: #6c757d;
        }
        
        button.danger-btn {
            background: #dc3545;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .invalid {
            border-color: #dc3545 !important;
            background-color: #fff5f5;
        }
        
        .empty-state {
            text-align: center;
            color: #666;
            padding: 20px;
        }
        
        .inventory-list,
        .piece-list {
            list-style: none;
            padding: 0;
        }
        
        .inventory-item,
        .piece-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            background: #f9f9f9;
        }
        
        .item-info {
            flex: 1;
        }
        
        .item-name {
            font-weight: bold;
            display: block;
        }
        
        .item-details {
            color: #666;
            font-size: 0.9em;
        }
        
        .item-actions {
            display: flex;
            gap: 5px;
        }
        
        .item-actions button {
            padding: 5px 10px;
            font-size: 0.8em;
        }
        
        .edit-btn {
            background: #28a745;
        }
        
        .duplicate-btn {
            background: #17a2b8;
        }
        
        .delete-btn {
            background: #dc3545;
        }
        
        .piece-summary {
            margin-top: 15px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 4px;
            font-weight: bold;
        }
        
        #visualizationCanvas {
            border: 1px solid #ddd;
            max-width: 100%;
            height: auto;
        }
        
        .sheet-navigation {
            margin: 10px 0;
            text-align: center;
        }
        
        .sheet-navigation button {
            margin: 0 5px;
        }
        
        #optimizationMessage {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .test-status {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .test-passed {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .test-failed {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-status" id="testStatus">
        <h2>Refactoring Test Status</h2>
        <p id="testMessage">Initializing refactored application...</p>
    </div>

    <div class="container">
        <h1>Woodworking Cut Optimizer - Refactored Version</h1>
        <p>This page tests the refactored modular architecture.</p>
    </div>

    <!-- Project Selection -->
    <div class="container">
        <h2>Project Management</h2>
        <div id="projectSelectContainer">
            <!-- ProjectSelect component will be rendered here -->
        </div>
    </div>

    <!-- Material Inventory -->
    <div class="container">
        <h2>Material Inventory</h2>
        <div id="materialFormContainer">
            <!-- MaterialForm component will be rendered here -->
        </div>
        <div id="inventoryListContainer">
            <!-- InventoryList component will be rendered here -->
        </div>
    </div>

    <!-- Project Pieces -->
    <div class="container">
        <h2>Project Pieces</h2>
        <div id="pieceFormContainer">
            <!-- PieceForm component will be rendered here -->
        </div>
        <div id="pieceListContainer">
            <!-- PieceList component will be rendered here -->
        </div>
    </div>

    <!-- Project Settings -->
    <div class="container">
        <h2>Project Settings</h2>
        <div class="inline-inputs">
            <div class="form-group">
                <label for="projectName">Project Name</label>
                <input type="text" id="projectName" placeholder="My Project" />
            </div>
            <div class="form-group">
                <label for="sawKerf">Saw Kerf</label>
                <input type="number" id="sawKerf" value="3" step="0.1" />
            </div>
            <div class="form-group">
                <label for="kerfUnit">Kerf Unit</label>
                <select id="kerfUnit">
                    <option value="mm">mm</option>
                    <option value="cm">cm</option>
                    <option value="in">inches</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Optimization -->
    <div class="container">
        <h2>Optimization</h2>
        <button id="optimizeBtn">Optimize Cutting Layout</button>
        <div id="optimizationMessage"></div>
        
        <div class="sheet-navigation">
            <button id="prevSheetBtn" disabled>Previous Sheet</button>
            <span id="sheetIndicator">Sheet 0 of 0</span>
            <button id="nextSheetBtn" disabled>Next Sheet</button>
        </div>
        
        <canvas id="visualizationCanvas" width="600" height="400"></canvas>
        
        <div style="margin-top: 20px;">
            <button id="exportBtn">Export Cutting List</button>
            <button id="printBtn">Print Layout</button>
        </div>
    </div>

    <!-- Include the bin packing library -->
    <script src="https://cdn.jsdelivr.net/gh/jakesgordon/bin-packing@master/js/binpacking.js"></script>
    
    <!-- Include the refactored application -->
    <script type="module">
        // Test the refactored application
        import { WoodworkingApp } from './src/main.js';
        
        async function runTests() {
            const testStatus = document.getElementById('testStatus');
            const testMessage = document.getElementById('testMessage');
            
            try {
                testMessage.textContent = 'Creating application instance...';
                
                // Create the application with test configuration
                const app = new WoodworkingApp({
                    enableServerOptimization: false,
                    enableAuthentication: false,
                    autoSaveInterval: 5000, // 5 seconds for testing
                });
                
                testMessage.textContent = 'Initializing application...';
                
                // Initialize the application
                await app.initialize();
                
                testMessage.textContent = 'Application initialized successfully!';
                testStatus.className = 'test-status test-passed';
                
                // Make app available for debugging
                window.woodworkingApp = app;
                
                // Add some test data
                setTimeout(() => {
                    addTestData();
                }, 1000);
                
            } catch (error) {
                console.error('Test failed:', error);
                testMessage.textContent = `Test failed: ${error.message}`;
                testStatus.className = 'test-status test-failed';
            }
        }
        
        function addTestData() {
            const app = window.woodworkingApp;
            if (!app) return;
            
            console.log('Adding test data...');
            
            // This would add test materials and pieces
            // For now, just log that the app is ready
            console.log('Refactored application is ready for use!');
            console.log('Available methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(app)));
        }
        
        // Run tests when DOM is loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', runTests);
        } else {
            runTests();
        }
    </script>
</body>
</html>
