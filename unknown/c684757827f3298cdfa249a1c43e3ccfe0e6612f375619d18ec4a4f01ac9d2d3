/**
 * Piece List Component - Displays and manages project pieces
 */

import { ProjectPiece } from '../../types';

export interface PieceListProps {
  pieces: ProjectPiece[];
  onEdit: (piece: ProjectPiece) => void;
  onDelete: (id: string) => void;
  onDuplicate?: (id: string) => void;
}

export class PieceList {
  private container: HTMLElement;
  private props: PieceListProps;

  constructor(container: HTMLElement, props: PieceListProps) {
    this.container = container;
    this.props = props;
    this.render();
  }

  /**
   * Update props and re-render
   */
  updateProps(newProps: PieceListProps): void {
    this.props = newProps;
    this.render();
  }

  /**
   * Render the piece list
   */
  private render(): void {
    if (this.props.pieces.length === 0) {
      this.container.innerHTML = `
        <div class="empty-state">
          <p>No pieces in project. Add pieces above to get started.</p>
        </div>
      `;
      return;
    }

    const listHTML = this.props.pieces.map(piece => `
      <li class="piece-item" data-id="${piece.id}">
        <div class="item-info">
          <span class="item-name">${piece.name}</span>
          <span class="item-details">
            ${piece.quantity}x ${piece.length}${piece.unit} × ${piece.width}${piece.unit}
            ${piece.grainDirection !== 'none' ? `, Grain: ${this.formatGrainDirection(piece.grainDirection)}` : ''}
          </span>
        </div>
        <div class="item-actions">
          <button class="edit-btn" data-id="${piece.id}" aria-label="Edit ${piece.name}">
            Edit
          </button>
          ${this.props.onDuplicate ? `
            <button class="duplicate-btn" data-id="${piece.id}" aria-label="Duplicate ${piece.name}">
              Duplicate
            </button>
          ` : ''}
          <button class="delete-btn" data-id="${piece.id}" aria-label="Delete ${piece.name}">
            Delete
          </button>
        </div>
      </li>
    `).join('');

    this.container.innerHTML = `
      <ul class="piece-list">
        ${listHTML}
      </ul>
      <div class="piece-summary">
        <p>Total pieces: ${this.getTotalPieceCount()}</p>
      </div>
    `;

    this.bindEvents();
  }

  /**
   * Format grain direction for display
   */
  private formatGrainDirection(direction: string): string {
    switch (direction) {
      case 'length': return 'Along Length';
      case 'width': return 'Along Width';
      case 'none': return 'No Preference';
      default: return direction;
    }
  }

  /**
   * Bind event listeners
   */
  private bindEvents(): void {
    // Edit buttons
    this.container.querySelectorAll('.edit-btn').forEach(button => {
      button.addEventListener('click', (e) => {
        const id = (e.target as HTMLElement).dataset.id!;
        const piece = this.props.pieces.find(p => p.id === id);
        if (piece) {
          this.props.onEdit(piece);
        }
      });
    });

    // Delete buttons
    this.container.querySelectorAll('.delete-btn').forEach(button => {
      button.addEventListener('click', (e) => {
        const id = (e.target as HTMLElement).dataset.id!;
        const piece = this.props.pieces.find(p => p.id === id);
        if (piece && confirm(`Are you sure you want to delete "${piece.name}"?`)) {
          this.props.onDelete(id);
        }
      });
    });

    // Duplicate buttons
    if (this.props.onDuplicate) {
      this.container.querySelectorAll('.duplicate-btn').forEach(button => {
        button.addEventListener('click', (e) => {
          const id = (e.target as HTMLElement).dataset.id!;
          this.props.onDuplicate!(id);
        });
      });
    }
  }

  /**
   * Get total piece count (including quantities)
   */
  getTotalPieceCount(): number {
    return this.props.pieces.reduce((sum, piece) => sum + piece.quantity, 0);
  }

  /**
   * Get pieces grouped by name
   */
  getPiecesByName(): Record<string, ProjectPiece[]> {
    return this.props.pieces.reduce((groups, piece) => {
      const key = piece.name;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(piece);
      return groups;
    }, {} as Record<string, ProjectPiece[]>);
  }

  /**
   * Get total area of all pieces
   */
  getTotalArea(): { value: number; unit: string } {
    // Calculate total area in square mm for consistency
    let totalAreaMM = 0;
    
    this.props.pieces.forEach(piece => {
      const lengthMM = this.convertToMM(piece.length, piece.unit);
      const widthMM = this.convertToMM(piece.width, piece.unit);
      const areaMM = lengthMM * widthMM * piece.quantity;
      totalAreaMM += areaMM;
    });

    // Convert to most appropriate unit
    if (totalAreaMM < 1000000) { // Less than 1 m²
      return { value: totalAreaMM / 100, unit: 'cm²' };
    } else {
      return { value: totalAreaMM / 1000000, unit: 'm²' };
    }
  }

  /**
   * Convert dimension to mm
   */
  private convertToMM(value: number, unit: string): number {
    switch (unit) {
      case 'mm': return value;
      case 'cm': return value * 10;
      case 'in': return value * 25.4;
      default: return value;
    }
  }

  /**
   * Get piece statistics
   */
  getPieceStats(): {
    totalPieces: number;
    uniquePieces: number;
    totalArea: { value: number; unit: string };
    largestPiece: ProjectPiece | null;
    smallestPiece: ProjectPiece | null;
  } {
    if (this.props.pieces.length === 0) {
      return {
        totalPieces: 0,
        uniquePieces: 0,
        totalArea: { value: 0, unit: 'cm²' },
        largestPiece: null,
        smallestPiece: null,
      };
    }

    let largestPiece = this.props.pieces[0];
    let smallestPiece = this.props.pieces[0];
    let largestArea = 0;
    let smallestArea = Infinity;

    this.props.pieces.forEach(piece => {
      const lengthMM = this.convertToMM(piece.length, piece.unit);
      const widthMM = this.convertToMM(piece.width, piece.unit);
      const area = lengthMM * widthMM;

      if (area > largestArea) {
        largestArea = area;
        largestPiece = piece;
      }

      if (area < smallestArea) {
        smallestArea = area;
        smallestPiece = piece;
      }
    });

    return {
      totalPieces: this.getTotalPieceCount(),
      uniquePieces: this.props.pieces.length,
      totalArea: this.getTotalArea(),
      largestPiece,
      smallestPiece,
    };
  }

  /**
   * Destroy the component
   */
  destroy(): void {
    this.container.innerHTML = '';
  }
}
