/**
 * Project Manager - Handles project and piece management operations
 */

import { Project, ProjectPiece, StorageInterface, StorageError, AppEvents, Unit, GrainDirection } from '../types';
import { generateId, isPositiveNumber, EventEmitter } from '../utils';

export class ProjectManager extends EventEmitter<AppEvents> {
  private projects: Project[] = [];
  private currentProjectId: string | null = null;
  private storage: StorageInterface;

  constructor(storage: StorageInterface) {
    super();
    this.storage = storage;
  }

  /**
   * Initialize the manager by loading projects from storage
   */
  async initialize(): Promise<void> {
    try {
      // Migrate old data if needed
      await this.storage.migrateOldData();
      
      // Load projects and current project ID
      this.projects = await this.storage.getProjects();
      this.currentProjectId = await this.storage.getCurrentProjectId();

      // Create default project if none exist
      if (this.projects.length === 0) {
        await this.createDefaultProject();
      }

      // Validate current project ID
      if (this.currentProjectId && !this.projects.find(p => p.id === this.currentProjectId)) {
        this.currentProjectId = this.projects[0].id;
        await this.storage.setCurrentProjectId(this.currentProjectId);
      }

      // Set current project if none is set
      if (!this.currentProjectId && this.projects.length > 0) {
        this.currentProjectId = this.projects[0].id;
        await this.storage.setCurrentProjectId(this.currentProjectId);
      }
    } catch (error) {
      console.error('Failed to initialize ProjectManager:', error);
      this.projects = [];
      this.currentProjectId = null;
      throw error;
    }
  }

  /**
   * Get all projects
   */
  getProjects(): Project[] {
    return [...this.projects];
  }

  /**
   * Get current project
   */
  getCurrentProject(): Project | null {
    if (!this.currentProjectId) return null;
    return this.projects.find(p => p.id === this.currentProjectId) || null;
  }

  /**
   * Get current project ID
   */
  getCurrentProjectId(): string | null {
    return this.currentProjectId;
  }

  /**
   * Get a project by ID
   */
  getProject(id: string): Project | undefined {
    return this.projects.find(project => project.id === id);
  }

  /**
   * Create a new project
   */
  async createProject(name: string, sawKerf: number = 3, kerfUnit: Unit = 'mm'): Promise<Project> {
    if (!name || name.trim() === '') {
      throw new Error('Project name is required');
    }

    // Check for duplicate names
    if (this.projects.some(p => p.name.toLowerCase() === name.toLowerCase())) {
      throw new Error('A project with this name already exists');
    }

    if (!isPositiveNumber(sawKerf) && sawKerf !== 0) {
      throw new Error('Saw kerf must be a non-negative number');
    }

    const project: Project = {
      id: generateId(),
      name: name.trim(),
      pieces: [],
      sawKerf,
      kerfUnit,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.projects.push(project);

    try {
      await this.storage.saveProjects(this.projects);
      this.emit('project:created', project);
      return project;
    } catch (error) {
      // Rollback on save failure
      this.projects = this.projects.filter(p => p.id !== project.id);
      throw new StorageError(
        'Failed to save project to storage',
        'createProject',
        error
      );
    }
  }

  /**
   * Update an existing project
   */
  async updateProject(id: string, updates: Partial<Omit<Project, 'id' | 'createdAt'>>): Promise<Project> {
    const index = this.projects.findIndex(project => project.id === id);
    if (index === -1) {
      throw new Error(`Project with ID ${id} not found`);
    }

    const oldProject = { ...this.projects[index] };
    const updatedProject: Project = {
      ...oldProject,
      ...updates,
      id, // Ensure ID doesn't change
      createdAt: oldProject.createdAt, // Ensure createdAt doesn't change
      updatedAt: new Date(),
    };

    // Validate updates
    if (updates.name !== undefined) {
      if (!updates.name || updates.name.trim() === '') {
        throw new Error('Project name cannot be empty');
      }
      
      // Check for duplicate names (excluding current project)
      const duplicateName = this.projects.find(
        p => p.id !== id && p.name.toLowerCase() === updates.name!.toLowerCase()
      );
      if (duplicateName) {
        throw new Error('A project with this name already exists');
      }
    }

    if (updates.sawKerf !== undefined && (!isPositiveNumber(updates.sawKerf) && updates.sawKerf !== 0)) {
      throw new Error('Saw kerf must be a non-negative number');
    }

    this.projects[index] = updatedProject;

    try {
      await this.storage.saveProjects(this.projects);
      this.emit('project:updated', updatedProject);
      return updatedProject;
    } catch (error) {
      // Rollback on save failure
      this.projects[index] = oldProject;
      throw new StorageError(
        'Failed to update project in storage',
        'updateProject',
        error
      );
    }
  }

  /**
   * Delete a project
   */
  async deleteProject(id: string): Promise<void> {
    if (this.projects.length <= 1) {
      throw new Error('Cannot delete the last project');
    }

    const index = this.projects.findIndex(project => project.id === id);
    if (index === -1) {
      throw new Error(`Project with ID ${id} not found`);
    }

    const removedProject = this.projects[index];
    this.projects.splice(index, 1);

    // If deleting current project, switch to another
    if (this.currentProjectId === id) {
      this.currentProjectId = this.projects[0].id;
    }

    try {
      await this.storage.saveProjects(this.projects);
      await this.storage.setCurrentProjectId(this.currentProjectId);
      this.emit('project:deleted', id);
    } catch (error) {
      // Rollback on save failure
      this.projects.splice(index, 0, removedProject);
      if (this.currentProjectId !== id) {
        this.currentProjectId = id;
      }
      throw new StorageError(
        'Failed to delete project from storage',
        'deleteProject',
        error
      );
    }
  }

  /**
   * Switch to a different project
   */
  async switchToProject(id: string): Promise<Project> {
    const project = this.getProject(id);
    if (!project) {
      throw new Error(`Project with ID ${id} not found`);
    }

    this.currentProjectId = id;

    try {
      await this.storage.setCurrentProjectId(id);
      this.emit('project:loaded', project);
      return project;
    } catch (error) {
      throw new StorageError(
        'Failed to switch project in storage',
        'switchToProject',
        error
      );
    }
  }

  /**
   * Add a piece to the current project
   */
  async addPiece(pieceData: Omit<ProjectPiece, 'id'>): Promise<ProjectPiece> {
    const currentProject = this.getCurrentProject();
    if (!currentProject) {
      throw new Error('No current project selected');
    }

    this.validatePieceData(pieceData);

    const piece: ProjectPiece = {
      ...pieceData,
      id: generateId(),
      originalIndex: currentProject.pieces.length,
    };

    const updatedProject = {
      ...currentProject,
      pieces: [...currentProject.pieces, piece],
      updatedAt: new Date(),
    };

    await this.updateProject(currentProject.id, updatedProject);
    this.emit('piece:added', piece);
    return piece;
  }

  /**
   * Update a piece in the current project
   */
  async updatePiece(pieceId: string, pieceData: Omit<ProjectPiece, 'id'>): Promise<ProjectPiece> {
    const currentProject = this.getCurrentProject();
    if (!currentProject) {
      throw new Error('No current project selected');
    }

    this.validatePieceData(pieceData);

    const pieceIndex = currentProject.pieces.findIndex(p => p.id === pieceId);
    if (pieceIndex === -1) {
      throw new Error(`Piece with ID ${pieceId} not found`);
    }

    const updatedPiece: ProjectPiece = {
      ...pieceData,
      id: pieceId,
      originalIndex: currentProject.pieces[pieceIndex].originalIndex,
    };

    const updatedPieces = [...currentProject.pieces];
    updatedPieces[pieceIndex] = updatedPiece;

    const updatedProject = {
      ...currentProject,
      pieces: updatedPieces,
      updatedAt: new Date(),
    };

    await this.updateProject(currentProject.id, updatedProject);
    this.emit('piece:updated', updatedPiece);
    return updatedPiece;
  }

  /**
   * Remove a piece from the current project
   */
  async removePiece(pieceId: string): Promise<void> {
    const currentProject = this.getCurrentProject();
    if (!currentProject) {
      throw new Error('No current project selected');
    }

    const pieceIndex = currentProject.pieces.findIndex(p => p.id === pieceId);
    if (pieceIndex === -1) {
      throw new Error(`Piece with ID ${pieceId} not found`);
    }

    const updatedPieces = currentProject.pieces.filter(p => p.id !== pieceId);
    const updatedProject = {
      ...currentProject,
      pieces: updatedPieces,
      updatedAt: new Date(),
    };

    await this.updateProject(currentProject.id, updatedProject);
    this.emit('piece:deleted', pieceId);
  }

  /**
   * Create a default project
   */
  private async createDefaultProject(): Promise<Project> {
    const defaultProject = await this.createProject('My Project');
    this.currentProjectId = defaultProject.id;
    await this.storage.setCurrentProjectId(this.currentProjectId);
    return defaultProject;
  }

  /**
   * Validate piece data
   */
  private validatePieceData(pieceData: Omit<ProjectPiece, 'id'>): void {
    if (!pieceData.name || pieceData.name.trim() === '') {
      throw new Error('Piece name is required');
    }

    if (!isPositiveNumber(pieceData.length)) {
      throw new Error('Piece length must be a positive number');
    }

    if (!isPositiveNumber(pieceData.width)) {
      throw new Error('Piece width must be a positive number');
    }

    if (!isPositiveNumber(pieceData.quantity)) {
      throw new Error('Piece quantity must be a positive number');
    }

    if (!['mm', 'cm', 'in'].includes(pieceData.unit)) {
      throw new Error('Piece unit must be mm, cm, or in');
    }

    if (!['none', 'length', 'width'].includes(pieceData.grainDirection)) {
      throw new Error('Grain direction must be none, length, or width');
    }
  }

  /**
   * Duplicate a project
   */
  async duplicateProject(id: string, newName?: string): Promise<Project> {
    const original = this.getProject(id);
    if (!original) {
      throw new Error(`Project with ID ${id} not found`);
    }

    const name = newName || `${original.name} (Copy)`;
    
    const duplicated = await this.createProject(name, original.sawKerf, original.kerfUnit);
    
    // Add all pieces from original project
    for (const piece of original.pieces) {
      await this.addPiece({
        name: piece.name,
        length: piece.length,
        width: piece.width,
        unit: piece.unit,
        quantity: piece.quantity,
        grainDirection: piece.grainDirection,
      });
    }

    return duplicated;
  }
}
