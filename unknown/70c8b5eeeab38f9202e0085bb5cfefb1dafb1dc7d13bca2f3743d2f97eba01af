/**
 * Material Manager - Handles inventory management operations
 */

import { BaseMaterial, StorageInterface, StorageError, AppEvents } from '../types';
import { generateId, isPositiveNumber, EventEmitter } from '../utils';

export class MaterialManager extends EventEmitter<AppEvents> {
  private materials: BaseMaterial[] = [];
  private storage: StorageInterface;

  constructor(storage: StorageInterface) {
    super();
    this.storage = storage;
  }

  /**
   * Initialize the manager by loading materials from storage
   */
  async initialize(): Promise<void> {
    try {
      this.materials = await this.storage.getInventory();
    } catch (error) {
      console.error('Failed to initialize MaterialManager:', error);
      this.materials = [];
      throw error;
    }
  }

  /**
   * Get all materials
   */
  getMaterials(): BaseMaterial[] {
    return [...this.materials];
  }

  /**
   * Get a material by ID
   */
  getMaterial(id: string): BaseMaterial | undefined {
    return this.materials.find(material => material.id === id);
  }

  /**
   * Add a new material
   */
  async addMaterial(materialData: Omit<BaseMaterial, 'id'>): Promise<BaseMaterial> {
    this.validateMaterialData(materialData);

    const material: BaseMaterial = {
      ...materialData,
      id: generateId(),
    };

    this.materials.push(material);
    
    try {
      await this.storage.saveInventory(this.materials);
      this.emit('material:added', material);
      return material;
    } catch (error) {
      // Rollback on save failure
      this.materials = this.materials.filter(m => m.id !== material.id);
      throw new StorageError(
        'Failed to save material to storage',
        'addMaterial',
        error
      );
    }
  }

  /**
   * Update an existing material
   */
  async updateMaterial(id: string, materialData: Omit<BaseMaterial, 'id'>): Promise<BaseMaterial> {
    this.validateMaterialData(materialData);

    const index = this.materials.findIndex(material => material.id === id);
    if (index === -1) {
      throw new Error(`Material with ID ${id} not found`);
    }

    const oldMaterial = { ...this.materials[index] };
    const updatedMaterial: BaseMaterial = {
      ...materialData,
      id,
    };

    this.materials[index] = updatedMaterial;

    try {
      await this.storage.saveInventory(this.materials);
      this.emit('material:updated', updatedMaterial);
      return updatedMaterial;
    } catch (error) {
      // Rollback on save failure
      this.materials[index] = oldMaterial;
      throw new StorageError(
        'Failed to update material in storage',
        'updateMaterial',
        error
      );
    }
  }

  /**
   * Remove a material
   */
  async removeMaterial(id: string): Promise<void> {
    const index = this.materials.findIndex(material => material.id === id);
    if (index === -1) {
      throw new Error(`Material with ID ${id} not found`);
    }

    const removedMaterial = this.materials[index];
    this.materials.splice(index, 1);

    try {
      await this.storage.saveInventory(this.materials);
      this.emit('material:deleted', id);
    } catch (error) {
      // Rollback on save failure
      this.materials.splice(index, 0, removedMaterial);
      throw new StorageError(
        'Failed to remove material from storage',
        'removeMaterial',
        error
      );
    }
  }

  /**
   * Clear all materials
   */
  async clearMaterials(): Promise<void> {
    const oldMaterials = [...this.materials];
    this.materials = [];

    try {
      await this.storage.saveInventory(this.materials);
      oldMaterials.forEach(material => this.emit('material:deleted', material.id));
    } catch (error) {
      // Rollback on save failure
      this.materials = oldMaterials;
      throw new StorageError(
        'Failed to clear materials from storage',
        'clearMaterials',
        error
      );
    }
  }

  /**
   * Get materials by name (case-insensitive search)
   */
  findMaterialsByName(name: string): BaseMaterial[] {
    const searchTerm = name.toLowerCase();
    return this.materials.filter(material => 
      material.name.toLowerCase().includes(searchTerm)
    );
  }

  /**
   * Get materials that can fit a given piece size
   */
  findMaterialsForPiece(
    pieceLength: number,
    pieceWidth: number,
    pieceUnit: string,
    sawKerf: number = 0,
    kerfUnit: string = 'mm'
  ): BaseMaterial[] {
    // This would use the convertToUnit utility to compare dimensions
    // For now, simplified implementation
    return this.materials.filter(material => {
      // Simple check - would need proper unit conversion
      return (
        (material.length >= pieceLength && material.width >= pieceWidth) ||
        (material.length >= pieceWidth && material.width >= pieceLength)
      );
    });
  }

  /**
   * Get total inventory value (if materials had cost)
   */
  getTotalQuantity(): number {
    return this.materials.reduce((total, material) => total + material.quantity, 0);
  }

  /**
   * Get materials grouped by type/name
   */
  getMaterialsGrouped(): Record<string, BaseMaterial[]> {
    return this.materials.reduce((groups, material) => {
      const key = material.name;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(material);
      return groups;
    }, {} as Record<string, BaseMaterial[]>);
  }

  /**
   * Duplicate a material with new quantity
   */
  async duplicateMaterial(id: string, newQuantity: number = 1): Promise<BaseMaterial> {
    const original = this.getMaterial(id);
    if (!original) {
      throw new Error(`Material with ID ${id} not found`);
    }

    if (!isPositiveNumber(newQuantity)) {
      throw new Error('Quantity must be a positive number');
    }

    const duplicated = await this.addMaterial({
      ...original,
      quantity: newQuantity,
      name: `${original.name} (Copy)`,
    });

    return duplicated;
  }

  /**
   * Validate material data
   */
  private validateMaterialData(materialData: Omit<BaseMaterial, 'id'>): void {
    if (!materialData.name || materialData.name.trim() === '') {
      throw new Error('Material name is required');
    }

    if (!isPositiveNumber(materialData.length)) {
      throw new Error('Material length must be a positive number');
    }

    if (!isPositiveNumber(materialData.width)) {
      throw new Error('Material width must be a positive number');
    }

    if (!isPositiveNumber(materialData.quantity)) {
      throw new Error('Material quantity must be a positive number');
    }

    if (!['mm', 'cm', 'in'].includes(materialData.unit)) {
      throw new Error('Material unit must be mm, cm, or in');
    }

    // Check for duplicate names (optional - might want to allow duplicates)
    const existingMaterial = this.materials.find(
      material => material.name.toLowerCase() === materialData.name.toLowerCase()
    );
    
    if (existingMaterial) {
      console.warn(`Material with name "${materialData.name}" already exists`);
    }
  }

  /**
   * Export materials to JSON
   */
  exportMaterials(): string {
    return JSON.stringify(this.materials, null, 2);
  }

  /**
   * Import materials from JSON
   */
  async importMaterials(jsonData: string, replaceExisting: boolean = false): Promise<void> {
    try {
      const importedMaterials = JSON.parse(jsonData) as BaseMaterial[];
      
      if (!Array.isArray(importedMaterials)) {
        throw new Error('Invalid JSON format: expected array of materials');
      }

      // Validate all materials before importing
      importedMaterials.forEach((material, index) => {
        try {
          this.validateMaterialData(material);
        } catch (error) {
          throw new Error(`Invalid material at index ${index}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      });

      if (replaceExisting) {
        this.materials = [];
      }

      // Add all materials
      for (const materialData of importedMaterials) {
        await this.addMaterial({
          name: materialData.name,
          length: materialData.length,
          width: materialData.width,
          unit: materialData.unit,
          quantity: materialData.quantity,
          thickness: materialData.thickness,
        });
      }
    } catch (error) {
      throw new Error(`Failed to import materials: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
