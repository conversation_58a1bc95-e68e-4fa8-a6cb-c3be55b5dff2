# Woodworking Cut Optimizer

A comprehensive web application for optimizing woodworking cuts with advanced project management and editing capabilities.

## Features

### 🔧 **Material & Piece Management**
- **Add Materials**: Define base materials with dimensions, quantities, and thickness
- **Add Pieces**: Create project pieces with grain direction preferences
- **Edit Functionality**: Click "Edit" to modify existing materials and pieces
- **Remove Items**: Delete materials and pieces with confirmation

### 📁 **Multiple Project Management**
- **Project Selection**: Switch between multiple projects using the dropdown
- **Create Projects**: Add new projects with unique names
- **Delete Projects**: Remove projects with confirmation (prevents deleting the last project)
- **Auto-Save**: All project data is automatically saved to localStorage

### 📊 **Cut Optimization**
- **Bin Packing Algorithm**: Advanced 2D bin packing for optimal material usage
- **Grain Direction**: Respects wood grain preferences for each piece
- **Saw Kerf**: Accounts for blade thickness in calculations
- **Visual Layout**: Interactive canvas showing cut layouts for each sheet

### 📋 **Export & Documentation**
- **Cutting Lists**: Detailed text output with measurements and instructions
- **Export Plans**: Download cutting plans as text files
- **Print Support**: Print-friendly cutting plans
- **Sheet Navigation**: Browse through multiple sheet layouts

## Technology Stack

- **TypeScript**: Type-safe JavaScript development
- **Vite**: Fast build tool and development server
- **HTML5 Canvas**: Interactive visualization
- **localStorage**: Client-side data persistence
- **CSS3**: Modern responsive styling

## Run Locally

**Prerequisites:** Node.js (v14 or higher)

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm run dev
   ```

3. **Open in browser:**
   Navigate to `http://localhost:5173`

## Build for Production

```bash
npm run build
```

The built files will be in the `dist/` directory.

## Usage Guide

### Getting Started
1. **Create a Project**: Enter a project name and click "Create Project"
2. **Add Materials**: Define your available base materials (plywood, lumber, etc.)
3. **Add Pieces**: List all the pieces you need to cut for your project
4. **Optimize**: Click "OPTIMIZE CUTTING PLAN" to generate the optimal layout
5. **Review**: Use the visualization and cutting list to plan your cuts
6. **Export**: Download or print the cutting plan for workshop use

### Editing Items
- Click the orange "Edit" button next to any material or piece
- Modify the values in the form
- Click "Update Material/Piece" to save changes
- The form automatically returns to "Add" mode

### Managing Projects
- Use the "Current Project" dropdown to switch between projects
- Each project maintains its own materials, pieces, and settings
- Projects are automatically saved and restored between sessions

## Data Storage

All data is stored locally in your browser using localStorage:
- **Materials**: Shared across all projects
- **Projects**: Each project stores its pieces and settings separately
- **Backward Compatibility**: Automatically migrates old single-project data

## Browser Compatibility

- Chrome/Chromium (recommended)
- Firefox
- Safari
- Edge

## License

This project is open source and available under the MIT License.
