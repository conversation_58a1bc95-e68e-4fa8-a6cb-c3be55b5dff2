/**
 * Inventory List Component - Displays and manages material inventory
 */

import { BaseMaterial } from '../../types';

export interface InventoryListProps {
  materials: BaseMaterial[];
  onEdit: (material: BaseMaterial) => void;
  onDelete: (id: string) => void;
  onDuplicate?: (id: string) => void;
}

export class InventoryList {
  private container: HTMLElement;
  private props: InventoryListProps;

  constructor(container: HTMLElement, props: InventoryListProps) {
    this.container = container;
    this.props = props;
    this.render();
  }

  /**
   * Update props and re-render
   */
  updateProps(newProps: InventoryListProps): void {
    this.props = newProps;
    this.render();
  }

  /**
   * Render the inventory list
   */
  private render(): void {
    if (this.props.materials.length === 0) {
      this.container.innerHTML = `
        <div class="empty-state">
          <p>No materials in inventory. Add materials above to get started.</p>
        </div>
      `;
      return;
    }

    const listHTML = this.props.materials.map(material => `
      <li class="inventory-item" data-id="${material.id}">
        <div class="item-info">
          <span class="item-name">${material.name}</span>
          <span class="item-details">
            ${material.quantity}x ${material.length}${material.unit} × ${material.width}${material.unit}
            ${material.thickness ? `, ${material.thickness}` : ''}
          </span>
        </div>
        <div class="item-actions">
          <button class="edit-btn" data-id="${material.id}" aria-label="Edit ${material.name}">
            Edit
          </button>
          ${this.props.onDuplicate ? `
            <button class="duplicate-btn" data-id="${material.id}" aria-label="Duplicate ${material.name}">
              Duplicate
            </button>
          ` : ''}
          <button class="delete-btn" data-id="${material.id}" aria-label="Delete ${material.name}">
            Delete
          </button>
        </div>
      </li>
    `).join('');

    this.container.innerHTML = `
      <ul class="inventory-list">
        ${listHTML}
      </ul>
    `;

    this.bindEvents();
  }

  /**
   * Bind event listeners
   */
  private bindEvents(): void {
    // Edit buttons
    this.container.querySelectorAll('.edit-btn').forEach(button => {
      button.addEventListener('click', (e) => {
        const id = (e.target as HTMLElement).dataset.id!;
        const material = this.props.materials.find(m => m.id === id);
        if (material) {
          this.props.onEdit(material);
        }
      });
    });

    // Delete buttons
    this.container.querySelectorAll('.delete-btn').forEach(button => {
      button.addEventListener('click', (e) => {
        const id = (e.target as HTMLElement).dataset.id!;
        const material = this.props.materials.find(m => m.id === id);
        if (material && confirm(`Are you sure you want to delete "${material.name}"?`)) {
          this.props.onDelete(id);
        }
      });
    });

    // Duplicate buttons
    if (this.props.onDuplicate) {
      this.container.querySelectorAll('.duplicate-btn').forEach(button => {
        button.addEventListener('click', (e) => {
          const id = (e.target as HTMLElement).dataset.id!;
          this.props.onDuplicate!(id);
        });
      });
    }
  }

  /**
   * Get total material count
   */
  getTotalCount(): number {
    return this.props.materials.reduce((sum, material) => sum + material.quantity, 0);
  }

  /**
   * Get materials by type
   */
  getMaterialsByType(): Record<string, BaseMaterial[]> {
    return this.props.materials.reduce((groups, material) => {
      const key = material.name;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(material);
      return groups;
    }, {} as Record<string, BaseMaterial[]>);
  }

  /**
   * Destroy the component
   */
  destroy(): void {
    this.container.innerHTML = '';
  }
}
