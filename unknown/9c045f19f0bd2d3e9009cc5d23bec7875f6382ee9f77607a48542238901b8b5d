/**
 * Authentication Manager - Handles user authentication and authorization
 */

import { 
  AuthCredentials, 
  AuthResponse, 
  UserInfo, 
  ApiClientInterface,
  AppEvents
} from '../types';
import { EventEmitter } from '../utils';

export class AuthManager extends EventEmitter<AppEvents> {
  private apiClient: ApiClientInterface | null = null;
  private currentUser: UserInfo | null = null;
  private isAuthenticated: boolean = false;
  private tokenRefreshInterval: NodeJS.Timeout | null = null;

  constructor(apiClient?: ApiClientInterface) {
    super();
    this.apiClient = apiClient || null;
  }

  /**
   * Set API client
   */
  setApiClient(apiClient: ApiClientInterface): void {
    this.apiClient = apiClient;
  }

  /**
   * Initialize authentication manager
   */
  async initialize(): Promise<void> {
    // Try to restore session from localStorage
    await this.restoreSession();
    
    // Set up token refresh if authenticated
    if (this.isAuthenticated) {
      this.setupTokenRefresh();
    }
  }

  /**
   * Login with credentials
   */
  async login(credentials: AuthCredentials): Promise<AuthResponse> {
    if (!this.apiClient) {
      return {
        success: false,
        error: 'API client not configured',
      };
    }

    try {
      const response = await this.apiClient.authenticate(credentials);
      
      if (response.success && response.token && response.user) {
        this.isAuthenticated = true;
        this.currentUser = response.user;
        
        // Store session data
        await this.storeSession(response.token, response.refreshToken);
        
        // Set up token refresh
        this.setupTokenRefresh();
        
        this.emit('auth:login', response.user);
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed',
      };
    }
  }

  /**
   * Logout
   */
  async logout(): Promise<void> {
    try {
      if (this.apiClient) {
        await this.apiClient.logout();
      }
    } catch (error) {
      console.warn('Logout API call failed:', error);
    } finally {
      this.isAuthenticated = false;
      this.currentUser = null;
      
      // Clear session data
      await this.clearSession();
      
      // Clear token refresh
      this.clearTokenRefresh();
      
      this.emit('auth:logout', null);
    }
  }

  /**
   * Check if user is authenticated
   */
  isUserAuthenticated(): boolean {
    return this.isAuthenticated;
  }

  /**
   * Get current user info
   */
  getCurrentUser(): UserInfo | null {
    return this.currentUser;
  }

  /**
   * Check if user has specific permission
   */
  hasPermission(permission: string): boolean {
    if (!this.currentUser) return false;
    return this.currentUser.permissions.includes(permission);
  }

  /**
   * Check if user has specific plan
   */
  hasPlan(plan: 'free' | 'pro' | 'enterprise'): boolean {
    if (!this.currentUser) return false;
    
    const planHierarchy = { free: 0, pro: 1, enterprise: 2 };
    const userPlanLevel = planHierarchy[this.currentUser.plan];
    const requiredPlanLevel = planHierarchy[plan];
    
    return userPlanLevel >= requiredPlanLevel;
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<boolean> {
    if (!this.apiClient) return false;

    try {
      const response = await this.apiClient.refreshToken();
      
      if (response.success && response.token) {
        // Update stored session
        await this.storeSession(response.token, response.refreshToken);
        return true;
      } else {
        // Refresh failed, logout
        await this.logout();
        return false;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      await this.logout();
      return false;
    }
  }

  /**
   * Store session data in localStorage
   */
  private async storeSession(token: string, refreshToken?: string): Promise<void> {
    try {
      const sessionData = {
        token,
        refreshToken,
        user: this.currentUser,
        timestamp: Date.now(),
      };
      
      localStorage.setItem('woodOptimizer.session', JSON.stringify(sessionData));
    } catch (error) {
      console.warn('Failed to store session:', error);
    }
  }

  /**
   * Restore session from localStorage
   */
  private async restoreSession(): Promise<void> {
    try {
      const sessionData = localStorage.getItem('woodOptimizer.session');
      if (!sessionData) return;

      const session = JSON.parse(sessionData);
      
      // Check if session is not too old (24 hours)
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      if (Date.now() - session.timestamp > maxAge) {
        await this.clearSession();
        return;
      }

      if (session.token && session.user && this.apiClient) {
        this.apiClient.setAuthToken(session.token);
        this.currentUser = session.user;
        this.isAuthenticated = true;
        
        // Try to refresh token to ensure it's still valid
        const refreshSuccess = await this.refreshToken();
        if (!refreshSuccess) {
          await this.clearSession();
        }
      }
    } catch (error) {
      console.warn('Failed to restore session:', error);
      await this.clearSession();
    }
  }

  /**
   * Clear session data
   */
  private async clearSession(): Promise<void> {
    try {
      localStorage.removeItem('woodOptimizer.session');
    } catch (error) {
      console.warn('Failed to clear session:', error);
    }
  }

  /**
   * Set up automatic token refresh
   */
  private setupTokenRefresh(): void {
    this.clearTokenRefresh();
    
    // Refresh token every 50 minutes (assuming 1-hour token expiry)
    this.tokenRefreshInterval = setInterval(async () => {
      await this.refreshToken();
    }, 50 * 60 * 1000);
  }

  /**
   * Clear token refresh interval
   */
  private clearTokenRefresh(): void {
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
      this.tokenRefreshInterval = null;
    }
  }

  /**
   * Register new user (if supported by API)
   */
  async register(credentials: AuthCredentials & { confirmPassword: string }): Promise<AuthResponse> {
    if (!this.apiClient) {
      return {
        success: false,
        error: 'API client not configured',
      };
    }

    // Basic validation
    if (credentials.password !== credentials.confirmPassword) {
      return {
        success: false,
        error: 'Passwords do not match',
      };
    }

    try {
      // This would call a registration endpoint
      const response = await this.apiClient.authenticate({
        ...credentials,
        // Add registration flag or use different endpoint
      });
      
      if (response.success && response.token && response.user) {
        this.isAuthenticated = true;
        this.currentUser = response.user;
        
        await this.storeSession(response.token, response.refreshToken);
        this.setupTokenRefresh();
        
        this.emit('auth:register', response.user);
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Registration failed',
      };
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<{ success: boolean; message?: string; error?: string }> {
    if (!this.apiClient) {
      return {
        success: false,
        error: 'API client not configured',
      };
    }

    try {
      // This would call a password reset endpoint
      // For now, return success
      return {
        success: true,
        message: 'Password reset email sent',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Password reset failed',
      };
    }
  }

  /**
   * Change password
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<{ success: boolean; error?: string }> {
    if (!this.apiClient || !this.isAuthenticated) {
      return {
        success: false,
        error: 'Not authenticated',
      };
    }

    try {
      // This would call a change password endpoint
      // For now, return success
      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Password change failed',
      };
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: Partial<UserInfo>): Promise<{ success: boolean; user?: UserInfo; error?: string }> {
    if (!this.apiClient || !this.isAuthenticated) {
      return {
        success: false,
        error: 'Not authenticated',
      };
    }

    try {
      // This would call an update profile endpoint
      const updatedUser = { ...this.currentUser, ...updates } as UserInfo;
      this.currentUser = updatedUser;
      
      // Update stored session
      if (this.apiClient.getAuthToken) {
        const token = this.apiClient.getAuthToken();
        if (token) {
          await this.storeSession(token);
        }
      }
      
      this.emit('auth:profile-updated', updatedUser);
      
      return {
        success: true,
        user: updatedUser,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Profile update failed',
      };
    }
  }

  /**
   * Get authentication status for UI
   */
  getAuthStatus(): {
    isAuthenticated: boolean;
    user: UserInfo | null;
    plan: string;
    permissions: string[];
  } {
    return {
      isAuthenticated: this.isAuthenticated,
      user: this.currentUser,
      plan: this.currentUser?.plan || 'free',
      permissions: this.currentUser?.permissions || [],
    };
  }
}
