/**
 * Utility functions for the Woodworking Cut Optimizer
 */

import { Unit, AppConfig } from '../types';

// Default configuration
export const DEFAULT_CONFIG: AppConfig = {
  baseUnit: "mm",
  defaultSawKerf: 3,
  defaultKerfUnit: "mm",
  pieceColors: [
    "#e74c3c", "#3498db", "#2ecc71", "#f1c40f", "#9b59b6",
    "#1abc9c", "#e67e22", "#d35400", "#c0392b", "#2980b9",
  ],
  apiEndpoint: undefined,
  enableServerOptimization: false,
  enableAuthentication: false,
  maxLocalProjects: 50,
  autoSaveInterval: 30000, // 30 seconds
};

/**
 * Generate a unique ID
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

/**
 * Convert between different units
 */
export function convertToUnit(value: number, fromUnit: Unit, toUnit: Unit): number {
  if (fromUnit === toUnit) return value;

  const factors: Record<Unit, number> = { 
    mm: 1, 
    cm: 10, 
    in: 25.4 
  };
  
  const valueInMm = value * factors[fromUnit];
  return valueInMm / factors[toUnit];
}

/**
 * Get a color for a piece based on its index
 */
export function getPieceColor(index: number, colors: string[] = DEFAULT_CONFIG.pieceColors): string {
  return colors[index % colors.length];
}

/**
 * Format a number to a specific number of decimal places
 */
export function formatNumber(value: number, decimals: number = 1): string {
  return value.toFixed(decimals);
}

/**
 * Validate that a value is a positive number
 */
export function isPositiveNumber(value: any): boolean {
  return typeof value === 'number' && !isNaN(value) && value > 0;
}

/**
 * Validate that a value is a non-negative number
 */
export function isNonNegativeNumber(value: any): boolean {
  return typeof value === 'number' && !isNaN(value) && value >= 0;
}

/**
 * Sanitize a string for use as a filename
 */
export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[^a-z0-9]/gi, '_')
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '');
}

/**
 * Deep clone an object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  const cloned = {} as T;
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  
  return cloned;
}

/**
 * Debounce function to limit how often a function can be called
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function to limit how often a function can be called
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Calculate the area of a rectangle
 */
export function calculateArea(length: number, width: number): number {
  return length * width;
}

/**
 * Calculate material efficiency percentage
 */
export function calculateEfficiency(usedArea: number, totalArea: number): number {
  if (totalArea === 0) return 0;
  return (usedArea / totalArea) * 100;
}

/**
 * Calculate waste percentage
 */
export function calculateWaste(usedArea: number, totalArea: number): number {
  return 100 - calculateEfficiency(usedArea, totalArea);
}

/**
 * Format bytes to human readable format
 */
export function formatBytes(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Check if a string is a valid email
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Generate a random color in hex format
 */
export function generateRandomColor(): string {
  return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0');
}

/**
 * Check if two rectangles overlap
 */
export function rectanglesOverlap(
  rect1: { x: number; y: number; width: number; height: number },
  rect2: { x: number; y: number; width: number; height: number }
): boolean {
  return !(
    rect1.x + rect1.width <= rect2.x ||
    rect2.x + rect2.width <= rect1.x ||
    rect1.y + rect1.height <= rect2.y ||
    rect2.y + rect2.height <= rect1.y
  );
}

/**
 * Sort pieces by area (largest first)
 */
export function sortPiecesByArea<T extends { length: number; width: number; unit: Unit }>(
  pieces: T[],
  baseUnit: Unit = DEFAULT_CONFIG.baseUnit
): T[] {
  return [...pieces].sort((a, b) => {
    const areaA = calculateArea(
      convertToUnit(a.length, a.unit, baseUnit),
      convertToUnit(a.width, a.unit, baseUnit)
    );
    const areaB = calculateArea(
      convertToUnit(b.length, b.unit, baseUnit),
      convertToUnit(b.width, b.unit, baseUnit)
    );
    return areaB - areaA; // Largest first
  });
}

/**
 * Sort pieces by largest dimension (for better bin packing)
 */
export function sortPiecesByLargestDimension<T extends { length: number; width: number; unit: Unit }>(
  pieces: T[],
  baseUnit: Unit = DEFAULT_CONFIG.baseUnit
): T[] {
  return [...pieces].sort((a, b) => {
    const maxDimA = Math.max(
      convertToUnit(a.length, a.unit, baseUnit),
      convertToUnit(a.width, a.unit, baseUnit)
    );
    const maxDimB = Math.max(
      convertToUnit(b.length, b.unit, baseUnit),
      convertToUnit(b.width, b.unit, baseUnit)
    );
    
    if (maxDimA !== maxDimB) {
      return maxDimB - maxDimA; // Largest dimension first
    }
    
    // If max dimensions are equal, sort by area
    const areaA = calculateArea(
      convertToUnit(a.length, a.unit, baseUnit),
      convertToUnit(a.width, a.unit, baseUnit)
    );
    const areaB = calculateArea(
      convertToUnit(b.length, b.unit, baseUnit),
      convertToUnit(b.width, b.unit, baseUnit)
    );
    return areaB - areaA;
  });
}

/**
 * Create a download link for text content
 */
export function downloadTextFile(content: string, filename: string): void {
  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const anchor = document.createElement('a');
  anchor.download = sanitizeFilename(filename);
  anchor.href = url;
  anchor.click();
  URL.revokeObjectURL(url);
}

/**
 * Simple event emitter for application events
 */
export class EventEmitter<T extends Record<string, any>> {
  private listeners: { [K in keyof T]?: Array<(data: T[K]) => void> } = {};

  on<K extends keyof T>(event: K, listener: (data: T[K]) => void): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event]!.push(listener);
  }

  off<K extends keyof T>(event: K, listener: (data: T[K]) => void): void {
    if (!this.listeners[event]) return;
    const index = this.listeners[event]!.indexOf(listener);
    if (index > -1) {
      this.listeners[event]!.splice(index, 1);
    }
  }

  emit<K extends keyof T>(event: K, data: T[K]): void {
    if (!this.listeners[event]) return;
    this.listeners[event]!.forEach(listener => listener(data));
  }

  removeAllListeners<K extends keyof T>(event?: K): void {
    if (event) {
      delete this.listeners[event];
    } else {
      this.listeners = {};
    }
  }
}
