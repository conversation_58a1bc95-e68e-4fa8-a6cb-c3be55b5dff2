// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String?  @unique
  name      String?
  avatar    String?
  plan      UserPlan @default(FREE)
  
  // Authentication
  password  String
  emailVerified DateTime?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  projects  Project[]
  materials Material[]
  optimizationResults OptimizationResult[]
  
  @@map("users")
}

model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  sawKerf     Float    @default(3.0)
  kerfUnit    Unit     @default(MM)
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  pieces      Piece[]
  optimizationResults OptimizationResult[]
  
  @@map("projects")
}

model Material {
  id          String   @id @default(cuid())
  name        String
  length      Float
  width       Float
  thickness   Float?
  unit        Unit     @default(MM)
  quantity    Int      @default(1)
  cost        Float?
  supplier    String?
  notes       String?
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  usedInOptimizations OptimizationMaterial[]
  
  @@map("materials")
}

model Piece {
  id          String      @id @default(cuid())
  name        String
  length      Float
  width       Float
  thickness   Float?
  unit        Unit        @default(MM)
  quantity    Int         @default(1)
  grainDirection GrainDirection @default(NONE)
  priority    Int         @default(1)
  notes       String?
  
  // Timestamps
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  // Relations
  projectId   String
  project     Project     @relation(fields: [projectId], references: [id], onDelete: Cascade)
  placedPieces PlacedPiece[]
  
  @@map("pieces")
}

model OptimizationResult {
  id              String   @id @default(cuid())
  totalSheets     Int
  totalWaste      Float
  efficiency      Float
  processingTime  Int      // milliseconds
  algorithm       String   @default("server-optimized")
  
  // Timestamps
  createdAt       DateTime @default(now())
  
  // Relations
  userId          String
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  projectId       String
  project         Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  materials       OptimizationMaterial[]
  sheets          Sheet[]
  
  @@map("optimization_results")
}

model OptimizationMaterial {
  id                    String             @id @default(cuid())
  materialSnapshot      Json               // Snapshot of material at optimization time
  
  // Relations
  optimizationResultId  String
  optimizationResult    OptimizationResult @relation(fields: [optimizationResultId], references: [id], onDelete: Cascade)
  materialId            String
  material              Material           @relation(fields: [materialId], references: [id], onDelete: Cascade)
  
  @@map("optimization_materials")
}

model Sheet {
  id                    String             @id @default(cuid())
  sheetIndex            Int
  widthUsed             Float
  heightUsed            Float
  materialSnapshot      Json               // Snapshot of base material
  
  // Relations
  optimizationResultId  String
  optimizationResult    OptimizationResult @relation(fields: [optimizationResultId], references: [id], onDelete: Cascade)
  placedPieces          PlacedPiece[]
  
  @@map("sheets")
}

model PlacedPiece {
  id              String  @id @default(cuid())
  x               Float
  y               Float
  packedWidth     Float
  packedHeight    Float
  rotation        Float   @default(0)
  color           String?
  pieceSnapshot   Json    // Snapshot of piece at optimization time
  
  // Relations
  sheetId         String
  sheet           Sheet   @relation(fields: [sheetId], references: [id], onDelete: Cascade)
  pieceId         String
  piece           Piece   @relation(fields: [pieceId], references: [id], onDelete: Cascade)
  
  @@map("placed_pieces")
}

// Enums
enum UserPlan {
  FREE
  PRO
  ENTERPRISE
}

enum Unit {
  MM
  CM
  M
  IN
  FT
}

enum GrainDirection {
  NONE
  HORIZONTAL
  VERTICAL
  EITHER
}
