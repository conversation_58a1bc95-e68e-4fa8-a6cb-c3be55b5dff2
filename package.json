{"name": "woodworking-optimizer", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.2.0", "react": "^18.3.0", "react-dom": "^18.3.0", "@supabase/supabase-js": "^2.39.0", "@supabase/ssr": "^0.1.0", "@prisma/client": "^5.10.0", "prisma": "^5.10.0", "d3": "^7.9.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "tailwind-merge": "^2.2.0", "lucide-react": "^0.363.0", "zod": "^3.22.0", "react-hook-form": "^7.50.0", "@hookform/resolvers": "^3.3.0", "zustand": "^4.5.0", "@tanstack/react-query": "^5.28.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "nanoid": "^5.0.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20.11.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/d3": "^7.4.3", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "typescript": "^5.3.0", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "@tailwindcss/forms": "^0.5.7"}}