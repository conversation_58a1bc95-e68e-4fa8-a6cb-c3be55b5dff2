<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Print & Export with Visualizations</title>
    <script src="https://cdn.jsdelivr.net/gh/jakesgordon/bin-packing@master/js/packer.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
</head>
<body>
    <h1>Print & Export Test</h1>
    <div id="status">Loading...</div>
    <div id="controls" style="margin: 20px 0;">
        <button id="testPrintBtn" disabled>Test Print</button>
        <button id="testExportBtn" disabled>Test Export</button>
    </div>
    <div id="visualizationContainer" style="width: 800px; height: 600px; border: 1px solid #ccc; margin: 20px 0;"></div>
    <div class="sheet-navigation">
        <button id="prevSheetBtn" disabled>Previous</button>
        <span id="sheetIndicator">Sheet 0 of 0</span>
        <button id="nextSheetBtn" disabled>Next</button>
    </div>
    
    <script type="module">
        import { WoodworkingApp } from './src/main.js';
        
        let app = null;
        
        async function setupTest() {
            const statusEl = document.getElementById('status');
            const testPrintBtn = document.getElementById('testPrintBtn');
            const testExportBtn = document.getElementById('testExportBtn');
            
            try {
                statusEl.textContent = 'Creating app...';
                
                // Create app instance
                app = new WoodworkingApp({
                    enableServerOptimization: false,
                    enableAuthentication: false,
                });
                
                statusEl.textContent = 'Initializing app...';
                await app.initialize();
                
                statusEl.textContent = 'Adding test data...';
                
                // Add test material
                await app.materialManager.addMaterial({
                    name: 'Test Plywood',
                    length: 1200,
                    width: 800,
                    unit: 'mm',
                    quantity: 5,
                    thickness: '18mm'
                });
                
                // Create test project
                const projectId = await app.projectManager.createProject('Test Print Project');
                await app.projectManager.setCurrentProject(projectId);
                
                // Add test pieces
                const project = app.projectManager.getCurrentProject();
                if (project) {
                    await app.projectManager.addPiece(project.id, {
                        name: 'Shelf',
                        length: 600,
                        width: 300,
                        unit: 'mm',
                        quantity: 4,
                        grainDirection: 'length'
                    });
                    
                    await app.projectManager.addPiece(project.id, {
                        name: 'Side Panel',
                        length: 800,
                        width: 400,
                        unit: 'mm',
                        quantity: 2,
                        grainDirection: 'length'
                    });
                    
                    await app.projectManager.addPiece(project.id, {
                        name: 'Back Panel',
                        length: 1000,
                        width: 600,
                        unit: 'mm',
                        quantity: 1,
                        grainDirection: 'width'
                    });
                }
                
                statusEl.textContent = 'Running optimization...';
                
                // Run optimization
                const currentProject = app.projectManager.getCurrentProject();
                const materials = app.materialManager.getMaterials();
                
                if (currentProject && materials.length > 0) {
                    const request = {
                        materials,
                        pieces: currentProject.pieces,
                        sawKerf: currentProject.sawKerf,
                        kerfUnit: currentProject.kerfUnit,
                        projectId: currentProject.id,
                    };
                    
                    const response = await app.optimizationClient.optimize(request);
                    
                    if (response.success) {
                        statusEl.textContent = 'SUCCESS: Ready to test print and export!';
                        statusEl.style.color = 'green';
                        testPrintBtn.disabled = false;
                        testExportBtn.disabled = false;
                        
                        // Set up test buttons
                        testPrintBtn.addEventListener('click', () => {
                            if (app && app.canvasRenderer) {
                                // Test the print functionality
                                app.handlePrint();
                            }
                        });
                        
                        testExportBtn.addEventListener('click', () => {
                            if (app && app.canvasRenderer) {
                                // Test the export functionality
                                app.handleExport();
                            }
                        });
                        
                    } else {
                        statusEl.textContent = `Optimization failed: ${response.error}`;
                        statusEl.style.color = 'red';
                    }
                } else {
                    statusEl.textContent = 'No project or materials found';
                    statusEl.style.color = 'red';
                }
                
            } catch (error) {
                statusEl.textContent = `Error: ${error.message}`;
                statusEl.style.color = 'red';
                console.error('Test error:', error);
            }
        }
        
        // Check if required libraries are available
        if (typeof Packer === 'undefined') {
            document.getElementById('status').textContent = 'ERROR: Packer library not loaded';
            document.getElementById('status').style.color = 'red';
        } else if (typeof d3 === 'undefined') {
            document.getElementById('status').textContent = 'ERROR: D3 library not loaded';
            document.getElementById('status').style.color = 'red';
        } else {
            setupTest();
        }
    </script>
</body>
</html>
